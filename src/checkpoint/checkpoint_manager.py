from __future__ import annotations

import shutil

from contextlib import asynccontextmanager

from dataclasses import dataclass
from pathlib import Path
from typing import Any, AsyncIterator, Dict, List, Optional, Tuple

# 已移除：进度快照命名空间（snapshot 功能废弃）

from src.common.utils.time_utils import format_human_datetime
from src.memory.store_utils import get_store_and_checkpointer
from src.nodes.common.store_manager import WriterStoreManager


def _safe_get(obj: Any, key: str, default: Any = None) -> Any:
    if obj is None:
        return default
    if isinstance(obj, dict):
        return obj.get(key, default)
    return getattr(obj, key, default)


def _find_in_obj(obj: Any, keys: List[str]) -> Optional[Any]:
    try:
        if isinstance(obj, dict):
            for k in keys:
                if k in obj and obj[k] is not None:
                    return obj[k]
            for v in obj.values():
                found = _find_in_obj(v, keys)
                if found is not None:
                    return found
    except Exception:
        pass
    return None


# 已移除：_has_key_deep（废弃，未使用）


@dataclass
class BookGroupSummary:
    book_id: str
    book_name: Optional[str]
    created_ts: Optional[str]
    updated_ts: Optional[str]
    representative_thread_id: str
    thread_ids: List[str]


@dataclass
class CheckpointSummary:
    checkpoint_id: str
    thread_id: str
    book_id: str
    ts: Optional[str]
    metadata: Dict[str, Any]
    parent_checkpoint_id: Optional[str] = None


class CheckpointManager:
    """
    LangGraph Checkpoint 封装：以 book_id 作为一等公民进行聚合与操作。

    约定：book_id 为首次创建项目时的 thread_id；后续同一本书的不同运行分支可能拥有不同的 thread_id，
    但均在其 checkpoint/values 或 metadata 中携带相同的 book_id。
    """

    def __init__(self, store, checkpointer):
        self.store = store
        self.checkpointer = checkpointer

    async def cleanup_checkpoints_without_plan(self, *, dry_run: bool = False) -> int:
        """
        清理“尚未生成策划案(WriterPlan)”的 checkpoint 历史。

        规则：
        - 扫描全部 checkpoint，按 book_id 聚合
        - 若该 book_id 在 WriterStore 中不存在策划案，则视为“无意义历史”，
          删除该 book_id 下的所有线程(thread_id)的 checkpoint 记录

        返回删除的线程数（dry_run 时为将要删除的线程数）。
        """
        # 聚合所有书籍分组
        groups = await self.list_books()
        if not groups:
            return 0

        total_deleted_threads = 0
        for g in groups:
            bid = g.book_id
            try:
                wsm = WriterStoreManager(book_id=bid, store=self.store)
                plan = await wsm.get_writer_plan()
            except Exception:
                plan = None

            if plan is None:
                # 无策划案：删除该书所有线程的 checkpoint
                for tid in g.thread_ids:
                    total_deleted_threads += 1
                    if dry_run:
                        print(f"[Dry-Run] 将删除无策划案历史：book_id={bid}, thread_id={tid}")
                        continue
                    try:
                        await self.checkpointer.adelete_thread(tid)
                    except Exception:
                        # 静默失败，继续下一条
                        pass

        return total_deleted_threads

    @classmethod
    @asynccontextmanager
    async def connect(cls) -> AsyncIterator["CheckpointManager"]:
        async with get_store_and_checkpointer() as (store, checkpointer):
            yield cls(store, checkpointer)

    async def list_books(self) -> List[BookGroupSummary]:
        """
        扫描所有 checkpoint，按 book_id 聚合，返回书籍列表。
        - 与 thread_id 解耦：仅用于恢复时作为参数；聚合与显示均以 book_id 为准。
        - book_name 若缺失，将从 WriterPlan 中补齐。
        """
        groups_map: Dict[str, Dict[str, Any]] = {}

        async for rec in self.checkpointer.alist({}):
            # thread_id 解析
            thread_id = _safe_get(rec, "thread_id")
            if not thread_id:
                cfg = _safe_get(rec, "config")
                configurable = _safe_get(cfg, "configurable")
                if isinstance(configurable, dict):
                    thread_id = configurable.get("thread_id")
                elif configurable is not None:
                    thread_id = _safe_get(configurable, "thread_id")
                if not thread_id and isinstance(cfg, dict):
                    thread_id = cfg.get("thread_id")
            if not thread_id:
                # 无法识别线程，跳过
                continue

            checkpoint = _safe_get(rec, "checkpoint", {}) or {}
            ts = checkpoint.get("ts") if isinstance(checkpoint, dict) else None

            # 解析 book_id / book_name
            book_id: Optional[str] = None
            book_name: Optional[str] = None
            if isinstance(checkpoint, dict):
                values = checkpoint.get("values") or checkpoint.get("state") or checkpoint
                book_id = _find_in_obj(values, ["book_id"]) or book_id
                book_name = _find_in_obj(values, ["book_name"]) or book_name
                if not book_id:
                    meta = (
                        checkpoint.get("metadata")
                        or _safe_get(rec, "metadata")
                        or (rec.get("metadata") if isinstance(rec, dict) else None)
                    )
                    if isinstance(meta, dict):
                        book_id = meta.get("book_id") or book_id
                        book_name = meta.get("book_name") or book_name

            bid = book_id or thread_id
            g = groups_map.get(bid)
            if g is None:
                g = {
                    "book_name": book_name,
                    "created_ts": ts,
                    "updated_ts": ts,
                    "rep_thread": thread_id,
                    "rep_ts": ts,
                    "threads": set([thread_id]),
                }
                groups_map[bid] = g
            else:
                if not g.get("book_name") and book_name:
                    g["book_name"] = book_name
                if ts and (not g.get("created_ts") or ts < g["created_ts"]):
                    g["created_ts"] = ts
                if ts and (not g.get("updated_ts") or ts > g["updated_ts"]):
                    g["updated_ts"] = ts
                if ts and (not g.get("rep_ts") or ts > g["rep_ts"]):
                    g["rep_ts"] = ts
                    g["rep_thread"] = thread_id
                g["threads"].add(thread_id)

        # 二次补齐书名
        for bid, g in groups_map.items():
            if not g.get("book_name") and bid:
                try:
                    wsm = WriterStoreManager(book_id=bid, store=self.store)
                    plan = await wsm.get_writer_plan()
                    if plan is not None:
                        g["book_name"] = plan.book_name
                except Exception:
                    pass

        groups: List[BookGroupSummary] = []
        for bid, g in groups_map.items():
            groups.append(
                BookGroupSummary(
                    book_id=bid,
                    book_name=g.get("book_name"),
                    created_ts=g.get("created_ts"),
                    updated_ts=g.get("updated_ts"),
                    representative_thread_id=g.get("rep_thread"),
                    thread_ids=sorted(list(g.get("threads", []))),
                )
            )

        groups.sort(key=lambda gg: gg.updated_ts or "", reverse=True)
        return groups

    async def list_checkpoints(self, book_id: str) -> List[CheckpointSummary]:
        """
        列出指定 book_id 的所有 checkpoint（可能跨多个 thread_id）。
        仅基于 state['resume_info']（最新一条）作为展示信息，不再兼容旧字段。
        """
        results: List[CheckpointSummary] = []

        async for rec in self.checkpointer.alist({}):
            thread_id = _safe_get(rec, "thread_id")
            if not thread_id:
                cfg = _safe_get(rec, "config")
                configurable = _safe_get(cfg, "configurable")
                if isinstance(configurable, dict):
                    thread_id = configurable.get("thread_id")
                elif configurable is not None:
                    thread_id = _safe_get(configurable, "thread_id")
                if not thread_id and isinstance(cfg, dict):
                    thread_id = cfg.get("thread_id")
            if not thread_id:
                continue

            checkpoint = _safe_get(rec, "checkpoint", {}) or {}
            if not isinstance(checkpoint, dict):
                continue

            values = checkpoint.get("values") or checkpoint.get("state") or checkpoint
            bid = _find_in_obj(values, ["book_id"]) or _safe_get(checkpoint, "metadata", {}).get("book_id")
            if not bid:
                bid = thread_id  # 回退策略：默认 thread_id 作为 book_id

            if bid != book_id:
                continue

            cp_id = checkpoint.get("id") or _safe_get(rec, "checkpoint_id") or checkpoint.get("checkpoint_id")
            if not cp_id:
                # 没有可用的 checkpoint_id，跳过
                continue

            ts = checkpoint.get("ts")
            # 从 resume_info 读取展示信息（取最后一条）
            meta = {}
            try:
                if isinstance(values, dict):
                    resume_list = values.get("resume_info")
                    if isinstance(resume_list, list) and resume_list:
                        last = resume_list[-1]
                        if isinstance(last, dict):
                            meta = last
            except Exception:
                meta = {}

            # 读取父 checkpoint id（若存在）
            parent_cp_id = (
                _safe_get(rec, "parent_checkpoint_id")
                or _safe_get(checkpoint, "parent_checkpoint_id")
                or _safe_get(checkpoint.get("metadata", {}), "parent_checkpoint_id")
            )
            results.append(
                CheckpointSummary(
                    checkpoint_id=cp_id,
                    thread_id=thread_id,
                    book_id=bid,
                    ts=ts,
                    metadata=meta,
                    parent_checkpoint_id=parent_cp_id,
                )
            )

        # 新→旧
        results.sort(key=lambda r: r.ts or "", reverse=True)
        return results

    # ===================== 删除整本书（按 book_id 聚合的所有数据） =====================

    async def delete_book(
        self,
        book_id: str,
        *,
        workspace_path: Path = Path("./workspace"),
        dry_run: bool = False,
    ) -> None:
        """
        删除与指定 book_id 相关的所有数据：
        - LangGraph Checkpoint：删除该书对应的所有线程（thread_id）的 checkpoint 记录
        - Writer Store：删除 ("writer_app_v2", book_id) 命名空间前缀下的所有键（包含 plan/settings/outline/scene/chapters 等及其向量分块）
        - Workspace 文件：删除 workspace 下任意书名目录中的 <book_id>/ 子目录
        """

        # 1) 汇总线程分支
        books = await self.list_books()
        target = next((b for b in books if b.book_id == book_id), None)
        threads = list(target.thread_ids) if target is not None else []

        # 2) 统计或删除 writer_app_v2/<book_id> 命名空间前缀下的所有键
        ns_prefix = ("writer_app_v2", book_id)
        store_total = 0
        scan_offset = 0
        while True:
            items = await self.store.asearch(ns_prefix, limit=1000, offset=scan_offset)
            if not items:
                break
            store_total += len(items)
            if not dry_run:
                for it in items:
                    try:
                        await self.store.adelete(it.namespace, it.key)
                    except Exception:
                        pass
            scan_offset += len(items)

        # 3) 统计或删除 workspace 下所有 */<book_id>/ 子目录
        ws_dirs = []
        try:
            if workspace_path.exists():
                for book_name_dir in workspace_path.iterdir():
                    if not book_name_dir.is_dir():
                        continue
                    cand = book_name_dir / book_id
                    if cand.exists() and cand.is_dir():
                        ws_dirs.append(str(cand))
                        if not dry_run:
                            shutil.rmtree(cand, ignore_errors=True)
        except Exception:
            pass

        # 4) 线程 checkpoint 删除
        if dry_run:
            # 仅打印即将删除的资源清单与计数
            print("\n[Dry-Run] 将要删除的资源：")
            print(f"- Checkpoint 线程数：{len(threads)} (按 thread_id 聚合)")
            print(f"- Store (writer_app_v2/{book_id}) 键总数：{store_total}")
            #
            if ws_dirs:
                print(f"- 工作区目录：{len(ws_dirs)} 个")
                for p in ws_dirs:
                    print(f"  · {p}")
            else:
                print("- 工作区目录：0 个")
            return

        # 非 dry-run：执行 checkpoint 删除
        if threads:
            for thread_id in threads:
                try:
                    await self.checkpointer.adelete_thread(thread_id)
                except Exception:
                    # 忽略个别分支清理失败，继续后续步骤
                    pass

    # ===================== 构建历史树（按 book_id 聚合父子关系） =====================

    async def build_checkpoint_tree(
        self, book_id: str
    ) -> Tuple[List[str], Dict[str, Dict[str, Any]], Dict[str, List[str]]]:
        """
        构建指定 book_id 的 checkpoint 历史树。

        Returns:
            roots: 根 checkpoint_id 列表（无父或父缺失）
            nodes: {checkpoint_id: {id, parent, ts, thread_id, metadata}}
            children: {parent_id: [child_id, ...]}
        """
        nodes: Dict[str, Dict[str, Any]] = {}
        children: Dict[str, List[str]] = {}

        async for rec in self.checkpointer.alist({}):
            checkpoint = _safe_get(rec, "checkpoint", {}) or {}
            if not isinstance(checkpoint, dict):
                continue
            values = checkpoint.get("values") or checkpoint.get("state") or checkpoint
            # 解析 book_id
            bid = _find_in_obj(values, ["book_id"]) or _safe_get(checkpoint, "metadata", {}).get("book_id")
            if not bid:
                bid = _safe_get(rec, "thread_id")
            if bid != book_id:
                continue

            # id / parent / thread
            cid = checkpoint.get("id") or _safe_get(rec, "checkpoint_id") or checkpoint.get("checkpoint_id")
            if not cid:
                continue
            pid = (
                _safe_get(rec, "parent_checkpoint_id")
                or _safe_get(checkpoint, "parent_checkpoint_id")
                or _safe_get(checkpoint.get("metadata", {}), "parent_checkpoint_id")
            )
            thread_id = _safe_get(rec, "thread_id")
            ts = checkpoint.get("ts")

            # 元数据：采用 resume_info 的最新条目
            metadata: Dict[str, Any] = {}
            try:
                if isinstance(values, dict):
                    resume_list = values.get("resume_info")
                    if isinstance(resume_list, list) and resume_list:
                        last = resume_list[-1]
                        if isinstance(last, dict):
                            metadata = last
            except Exception:
                metadata = {}

            nodes[cid] = {
                "id": cid,
                "parent": pid,
                "ts": ts,
                "thread_id": thread_id,
                "metadata": metadata,
            }
            if pid:
                children.setdefault(pid, []).append(cid)

        # 根：无 parent 或者 parent 不存在于 nodes
        roots = [cid for cid, n in nodes.items() if not n.get("parent") or n.get("parent") not in nodes]
        return roots, nodes, children

    # （移除）print_checkpoint_tree：改用 prompt_select_checkpoint_tree 内联打印

    async def prompt_select_checkpoint_tree(self, book_id: str) -> Optional[CheckpointSummary]:
        """
        以树的方式展示 checkpoint，并允许选择其一进行回退。
        返回 None 表示不回退（使用最新状态）。
        """
        roots, nodes, children = await self.build_checkpoint_tree(book_id)
        cp_list = await self.list_checkpoints(book_id)
        cp_map = {c.checkpoint_id: c for c in cp_list}

        # 深度优先按照时间排序收集顺序
        order: List[str] = []

        def _dfs_collect(cid: str):
            order.append(cid)
            for kid in sorted(children.get(cid, []), key=lambda k: nodes[k].get("ts") or ""):
                _dfs_collect(kid)

        for r in sorted(roots, key=lambda k: nodes[k].get("ts") or ""):
            _dfs_collect(r)

        if not order:
            print("\n该书暂无可用的历史 checkpoint，将从最新状态继续...\n")
            return None

        print("\n可选的历史 checkpoint（树形，新→旧按分支排序）：")
        # 打印树形，同时编号
        index_to_cid: Dict[int, str] = {}
        counter = 0

        def _summary(meta: Dict[str, Any]) -> str:
            if not isinstance(meta, dict):
                return "(无摘要)"
            return meta.get("summary") or (
                (meta.get("node") or "")
                + (f"[{meta.get('stage')}]" if meta.get("stage") else "")
                + (f" -> {meta.get('next')}" if meta.get("next") else "")
            )

        def _meta_brief(meta: Dict[str, Any]) -> str:
            if not isinstance(meta, dict):
                return ""
            keys = [
                "node",
                "stage",
                "next",
                "type",
                "step_title",
                "step_index",
                "is_last_step",
                "target",
                "target_volume",
                "current_chapter",
                "chapter_index",
                "next_chapter",
                "buffer_len",
            ]
            pairs = []
            for k in keys:
                v = meta.get(k)
                if v is not None and v != "":
                    pairs.append(f"{k}={v}")
            return (" | " + ", ".join(pairs)) if pairs else ""

        def _dfs_print(cid: str, prefix: str, is_last: bool):
            nonlocal counter
            node = nodes[cid]
            counter += 1
            idx = counter
            index_to_cid[idx] = cid
            branch = "└─ " if is_last else "├─ "
            ts_h = format_human_datetime(node.get("ts"))
            tid = node.get("thread_id") or ""
            meta = node.get("metadata", {})
            print(f"{idx}. {prefix}{branch}{cid} @ {ts_h} (thread: {tid}) | {_summary(meta)}{_meta_brief(meta)}")
            kids = children.get(cid, [])
            for i, kid in enumerate(sorted(kids, key=lambda k: nodes[k].get("ts") or "")):
                next_prefix = prefix + ("   " if is_last else "│  ")
                _dfs_print(kid, next_prefix, i == len(kids) - 1)

        for r in sorted(roots, key=lambda k: nodes[k].get("ts") or ""):
            _dfs_print(r, "", True)

        print("0. 使用最新状态（不回退）")
        while True:
            try:
                choice = input(f"\n请选择 checkpoint (0-{len(index_to_cid)}): ").strip()
                if choice == "0":
                    return None
                idx = int(choice)
                if 1 <= idx <= len(index_to_cid):
                    cid = index_to_cid[idx]
                    if cid in cp_map:
                        return cp_map[cid]
                    n = nodes[cid]
                    return CheckpointSummary(
                        checkpoint_id=cid,
                        thread_id=n.get("thread_id"),
                        book_id=book_id,
                        ts=n.get("ts"),
                        metadata=n.get("metadata", {}),
                        parent_checkpoint_id=n.get("parent"),
                    )
                print(f"无效选择，请输入 0-{len(index_to_cid)} 之间的数字")
            except ValueError as e:
                print(f"请输入有效的数字, 当前输入: {choice}, 错误信息: {e}")
            except KeyboardInterrupt:
                print("\n用户取消，默认使用最新状态...")
                return None

    # ===================== 交互与格式化（统一输出/输入） =====================

    @staticmethod
    def format_book_line(index: int, book: BookGroupSummary) -> str:
        created = format_human_datetime(book.created_ts)
        updated = format_human_datetime(book.updated_ts)
        name = book.book_name or "(未命名)"
        return f"{index}. 【{name}】{book.book_id} 创建时间：{created}，更新时间：{updated}"

    @staticmethod
    def format_checkpoint_line(index: int, checkpoint: "CheckpointSummary") -> str:
        """
        以单行形式展示 checkpoint 概览信息：时间、人类可读摘要与少量关键信息。
        """
        ts_h = format_human_datetime(checkpoint.ts)
        meta = checkpoint.metadata or {}

        def _summary(meta_obj: Dict[str, Any]) -> str:
            if not isinstance(meta_obj, dict):
                return "(无摘要)"
            return meta_obj.get("summary") or (
                (meta_obj.get("node") or "")
                + (f"[{meta_obj.get('stage')}]" if meta_obj.get("stage") else "")
                + (f" -> {meta_obj.get('next')}" if meta_obj.get("next") else "")
            )

        def _meta_brief(meta_obj: Dict[str, Any]) -> str:
            if not isinstance(meta_obj, dict):
                return ""
            keys = [
                "node",
                "stage",
                "next",
                "type",
                "step_title",
                "step_index",
                "is_last_step",
                "target",
                "target_volume",
                "current_chapter",
                "chapter_index",
                "next_chapter",
                "buffer_len",
            ]
            pairs = []
            for k in keys:
                v = meta_obj.get(k)
                if v is not None and v != "":
                    pairs.append(f"{k}={v}")
            return (" | " + ", ".join(pairs)) if pairs else ""

        return (
            f"{index}. {checkpoint.checkpoint_id} @ {ts_h} (thread: {checkpoint.thread_id}) | "
            f"{_summary(meta)}{_meta_brief(meta)}"
        )

    # （移除）prompt_select_book：外部 CLI 已自定义菜单

    @staticmethod
    def prompt_select_branch(group: BookGroupSummary) -> str:
        if len(group.thread_ids) <= 1:
            return group.representative_thread_id
        print("\n检测到该书籍存在多个线程（分支）：")
        for i, tid in enumerate(group.thread_ids, start=1):
            flag = "（代表）" if tid == group.representative_thread_id else ""
            print(f"{i}. {tid} {flag}")
        print("0. 使用代表线程（最新）")
        while True:
            try:
                choice = input(f"\n请选择分支 (0-{len(group.thread_ids)}): ").strip()
                if choice == "0":
                    return group.representative_thread_id
                idx = int(choice)
                if 1 <= idx <= len(group.thread_ids):
                    return group.thread_ids[idx - 1]
                print(f"无效选择，请输入 0-{len(group.thread_ids)} 之间的数字")
            except ValueError as e:
                print(f"请输入有效的数字, 当前输入: {choice}, 错误信息: {e}")
            except KeyboardInterrupt:
                print("\n用户取消，默认使用代表线程...")
                return group.representative_thread_id

    # （移除）prompt_select_checkpoint：改用树形选择

    # （移除）build_resume_config：外部直接构建 config

    async def prompt_select_book(self) -> "BookSelectionResult":
        """
        交互式选择书籍与恢复方式：
        - 返回 BookSelectionResult，供上层 CLI（apps/main.py）根据结果执行新建、删除或恢复。
        - 当数据库无任何历史数据时，返回 action="empty" 提示上层处理（例如自动新建）。
        - 删除操作仅返回意图与目标 book_id，由调用方负责执行删除（保持职责边界）。
        """
        books = await self.list_books()
        if not books:
            print("\n📚 暂无进行中的小说项目。\n")
            return BookSelectionResult(action="empty")

        while True:
            print("\n" + "=" * 80)
            print("📚 发现以下进行中的小说项目:")
            print("=" * 80)
            for i, b in enumerate(books, start=1):
                print(self.format_book_line(i, b))
            print("\n0. 创建新的小说项目")
            print("D. 删除小说项目")

            try:
                choice = input(f"\n请输入选择 (0-{len(books)} / D): ").strip().lower()
            except KeyboardInterrupt:
                print("\n用户取消，默认新建项目...")
                return BookSelectionResult(action="new")

            if choice == "0":
                return BookSelectionResult(action="new")

            if choice == "d":
                # 删除流程（仅返回意图与对象，由上层执行删除）
                print("\n请选择要删除的项目：")
                for i, b in enumerate(books, start=1):
                    print(self.format_book_line(i, b))
                print("0. 取消删除")
                try:
                    del_choice = input(f"\n请输入选择 (0-{len(books)}): ").strip()
                except KeyboardInterrupt:
                    print("\n已取消删除")
                    continue
                if del_choice == "0":
                    print("已取消删除")
                    continue
                try:
                    idx = int(del_choice)
                    if not (1 <= idx <= len(books)):
                        print("无效选择")
                        continue
                except ValueError:
                    print("请输入有效数字")
                    continue
                target = books[idx - 1]
                confirm = (
                    input(
                        f"\n确认删除【{target.book_name or '(未命名)'}】(book_id={target.book_id}) 的全部数据？此操作不可恢复！(y/N): "
                    )
                    .strip()
                    .lower()
                )
                if confirm != "y":
                    print("已取消删除")
                    continue
                return BookSelectionResult(action="delete", book_id=target.book_id, book_name=target.book_name)

            # 恢复流程
            try:
                idx = int(choice)
                if not (1 <= idx <= len(books)):
                    print(f"无效选择，请输入 0-{len(books)} 之间的数字或 D")
                    continue
            except ValueError as e:
                print(f"请输入有效的数字或 D, 当前输入: {choice}, 错误信息: {e}")
                continue

            selected = books[idx - 1]

            # 默认使用“树形方式”选择 checkpoint 回退
            chosen_cp = await self.prompt_select_checkpoint_tree(selected.book_id)
            if chosen_cp is not None:
                return BookSelectionResult(
                    action="resume",
                    book_id=selected.book_id,
                    book_name=selected.book_name,
                    thread_id=chosen_cp.thread_id,
                    checkpoint_id=chosen_cp.checkpoint_id,
                )

            # 否则按线程分支恢复（不回退）
            thread_id = self.prompt_select_branch(selected)
            return BookSelectionResult(
                action="resume",
                book_id=selected.book_id,
                book_name=selected.book_name,
                thread_id=thread_id,
            )


# ===================== 选择结果类型与选择逻辑（解耦 CLI） =====================


@dataclass
class BookSelectionResult:
    """
    书籍选择结果：
    - action: "new" | "resume" | "delete" | "empty"
    - 当 action=="resume" 时，至少携带 thread_id；如用户选择了特定 checkpoint，还会携带 checkpoint_id。
    - 当 action=="delete" 时，携带 book_id（由调用方执行删除逻辑）。
    - 当 action=="empty" 时，表示数据库中无任何历史项目，上层可据此选择自动新建。
    """

    action: str
    book_id: Optional[str] = None
    book_name: Optional[str] = None
    thread_id: Optional[str] = None
    checkpoint_id: Optional[str] = None


class CheckpointManager(CheckpointManager):
    async def get_latest_state(self, book_id: str) -> Optional[Dict[str, Any]]:
        """
        获取指定book_id的最新状态。
        """
        try:
            # 获取该book_id的所有checkpoints
            checkpoints = await self.list_checkpoints(book_id)
            if not checkpoints:
                return None

            # 获取最新的checkpoint
            latest_checkpoint = checkpoints[0]  # list_checkpoints已经按时间倒序排列

            # 通过checkpointer获取完整的checkpoint数据
            config = {
                "configurable": {
                    "thread_id": latest_checkpoint.thread_id,
                    "checkpoint_id": latest_checkpoint.checkpoint_id,
                }
            }

            checkpoint_data = await self.checkpointer.aget(config)
            if checkpoint_data is None:
                return None

            # 提取channel_values作为状态
            if hasattr(checkpoint_data, "checkpoint"):
                checkpoint = checkpoint_data.checkpoint
            else:
                checkpoint = checkpoint_data

            if isinstance(checkpoint, dict):
                return checkpoint.get("channel_values", {})

            return None
        except Exception as e:
            print(f"获取最新状态失败: {e}")
            return None

    async def prompt_select_book(self) -> BookSelectionResult:
        """
        交互式选择书籍与恢复方式：
        - 返回 BookSelectionResult，供上层 CLI（apps/main.py）根据结果执行新建、删除或恢复。
        - 当数据库无任何历史数据时，返回 action="empty" 提示上层处理（例如自动新建）。
        - 删除操作仅返回意图与目标 book_id，由调用方负责执行删除（保持职责边界）。
        """
        books = await self.list_books()
        if not books:
            print("\n📚 暂无进行中的小说项目。\n")
            return BookSelectionResult(action="empty")

        while True:
            print("\n" + "=" * 80)
            print("📚 发现以下进行中的小说项目:")
            print("=" * 80)
            for i, b in enumerate(books, start=1):
                print(self.format_book_line(i, b))
            print("\n0. 创建新的小说项目")
            print("D. 删除小说项目")

            try:
                choice = input(f"\n请输入选择 (0-{len(books)} / D): ").strip().lower()
            except KeyboardInterrupt:
                print("\n用户取消，默认新建项目...")
                return BookSelectionResult(action="new")

            if choice == "0":
                return BookSelectionResult(action="new")

            if choice == "d":
                # 删除流程（仅返回意图与对象，由上层执行删除）
                print("\n请选择要删除的项目：")
                for i, b in enumerate(books, start=1):
                    print(self.format_book_line(i, b))
                print("0. 取消删除")
                try:
                    del_choice = input(f"\n请输入选择 (0-{len(books)}): ").strip()
                except KeyboardInterrupt:
                    print("\n已取消删除")
                    continue
                if del_choice == "0":
                    print("已取消删除")
                    continue
                try:
                    idx = int(del_choice)
                    if not (1 <= idx <= len(books)):
                        print("无效选择")
                        continue
                except ValueError:
                    print("请输入有效数字")
                    continue
                target = books[idx - 1]
                confirm = (
                    input(
                        f"\n确认删除【{target.book_name or '(未命名)'}】(book_id={target.book_id}) 的全部数据？此操作不可恢复！(y/N): "
                    )
                    .strip()
                    .lower()
                )
                if confirm != "y":
                    print("已取消删除")
                    continue
                return BookSelectionResult(action="delete", book_id=target.book_id, book_name=target.book_name)

            # 恢复流程
            try:
                idx = int(choice)
                if not (1 <= idx <= len(books)):
                    print(f"无效选择，请输入 0-{len(books)} 之间的数字或 D")
                    continue
            except ValueError as e:
                print(f"请输入有效的数字或 D, 当前输入: {choice}, 错误信息: {e}")
                continue

            selected = books[idx - 1]

            # 默认使用“树形方式”选择 checkpoint 回退
            chosen_cp = await self.prompt_select_checkpoint_tree(selected.book_id)
            if chosen_cp is not None:
                return BookSelectionResult(
                    action="resume",
                    book_id=selected.book_id,
                    book_name=selected.book_name,
                    thread_id=chosen_cp.thread_id,
                    checkpoint_id=chosen_cp.checkpoint_id,
                )

            # 否则按线程分支恢复（不回退）
            thread_id = self.prompt_select_branch(selected)
            return BookSelectionResult(
                action="resume",
                book_id=selected.book_id,
                book_name=selected.book_name,
                thread_id=thread_id,
            )
