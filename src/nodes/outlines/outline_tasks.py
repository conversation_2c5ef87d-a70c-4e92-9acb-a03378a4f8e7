from __future__ import annotations

from typing import Literal, Optional

from langchain_core.messages import BaseMessage
from langgraph.config import RunnableConfig
from langgraph.types import Command, Send

from src.context.context_builder import ContextBuilder
from src.nodes.common.book_types import BookDetail, CharacterDetailCollection, VolumeOutline, WriterPlan
from src.nodes.common.node_kit import build_resume_info, llm_json, maybe_interrupt_review, write_text_under_book
from src.nodes.common.store_manager import WriterStoreManager
from src.nodes.outlines.outline_prompt import bo_detail_prompt_template
from src.state import State


def _build_refine_messages(state: State, config: RunnableConfig, target_volume: VolumeOutline) -> list[BaseMessage]:
    character_details: CharacterDetailCollection = state.get("character_details")
    character_content = character_details.get_characters_content() if character_details else ""
    refined_book_outlines = state.get("book_detail").get_completed_volume_outlines()

    prompt_text = bo_detail_prompt_template.format(
        refined_book_setting=state.get("refined_book_setting"),
        character_design=character_content,
        refined_book_outlines=refined_book_outlines,
        volume_index=target_volume.volume_number,
    )

    cb = ContextBuilder()
    cb.header()
    cb.section("大纲细化任务", prompt_text)
    cb.json_schema(VolumeOutline.model_json_schema())

    budget = config.get("configurable", {}).get("prompt_budget_tokens")
    return cb.build(max_tokens=budget, policy="outline")


async def writer_outline_refine_one_task(
    state: State, config: RunnableConfig
) -> Command[Literal["writer_outline_join"]]:
    """
    子任务：细化单个卷大纲。
    读取 state.task_target_index，并将结果写入 `outline_task_result_{i}`。
    """
    book_detail: BookDetail = state.get("book_detail")
    idx: int = int(state.get("task_target_index"))
    target_vol = book_detail.volume_outlines[idx]

    msgs = _build_refine_messages(state, config, target_vol)
    refined_one: VolumeOutline = await llm_json("writer_outline", msgs, VolumeOutline, config=config)
    refined_one.version = target_vol.version + 1
    refined_one.volume_number = target_vol.volume_number
    refined_one.volume_title = target_vol.volume_title

    # 落盘单卷
    book_name = state.get("book_name")
    book_id = state.get("book_id")
    file_rel_one = f"outline/第{refined_one.volume_number}卷-细化大纲-v{refined_one.version}.md"
    _ = write_text_under_book(book_name, book_id, file_rel_one, refined_one.get_completed_contents())

    key = f"outline_task_result_{idx}"
    update = {
        key: {
            "volume_number": refined_one.volume_number,
            "volume_title": refined_one.volume_title,
            "version": refined_one.version,
            "refined": refined_one.model_dump(),
        }
    }
    return Command(update=update, goto="writer_outline_join")


async def writer_outline_spawn_tasks(
    state: State, target_indices: list[int], current_volume_number: int, max_required_volume: int
) -> Command[Literal["writer_outline_join"]]:
    tasks = [Send("writer_outline_refine_one", {"task_target_index": i}) for i in target_indices]
    update = {
        "outline_task_current_volume_number": current_volume_number,
        "outline_task_max_required_volume": max_required_volume,
        "outline_task_expected_indices": target_indices,
    }
    return Command(update=update, goto=tasks + ["writer_outline_join"])


async def writer_outline_join_node(
    state: State, config: RunnableConfig
) -> Command[Literal["writer_outline", "writer_scene"]]:
    """
    聚合子任务结果，写回 `book_detail`，生成批量审阅文件，触发 interrupt 审核。
    """
    book_detail: BookDetail = state.get("book_detail")
    book_id = state.get("book_id")
    book_name = state.get("book_name")

    # 收集结果
    result_items: list[tuple[int, VolumeOutline]] = []
    expected: set[int] = set()
    try:
        expected = set(int(x) for x in (state.get("outline_task_expected_indices") or []))
    except Exception:
        expected = set()
    for k, v in list(state.items()):
        if isinstance(k, str) and k.startswith("outline_task_result_") and isinstance(v, dict):
            try:
                idx = int(k.split("_")[-1])
            except Exception:
                continue
            try:
                refined = VolumeOutline.model_validate(v.get("refined"))
            except Exception:
                continue
            # 写回到 book_detail 的对应位置
            if 0 <= idx < len(book_detail.volume_outlines):
                book_detail.volume_outlines[idx] = refined
                result_items.append((idx, refined))

    # 若未全部完成，则等待（再次进入 join）
    if expected and (set([idx for idx, _ in result_items]) != expected):
        return Command(goto="writer_outline_join")

    # 批量文件
    current_volume_number = int(state.get("outline_task_current_volume_number", 1))
    max_required_volume = int(state.get("outline_task_max_required_volume", current_volume_number))
    batch_file_rel = f"outline/批量-第{current_volume_number}~{max_required_volume}卷-细化大纲.md"
    lines = ["# 卷细化批量更新", "", f"范围：第{current_volume_number}~{max_required_volume}卷", "", "---", ""]
    # 以卷号排序
    result_items.sort(key=lambda x: x[1].volume_number)
    for _, refined in result_items:
        lines.append(f"## 第{refined.volume_number}卷 {refined.volume_title}")
        lines.append("")
        lines.append(refined.get_completed_contents())
        lines.append("")
    batch_content = "\n".join(lines)
    review_file = write_text_under_book(book_name, book_id, batch_file_rel, batch_content)

    update = {"book_detail": book_detail, "outline_last_volume_number": result_items[-1][1].volume_number}

    # 入库（一次）
    try:
        if book_id:
            sm = WriterStoreManager(book_id=book_id)
            await sm.store_volume_outline(book_detail)
    except RuntimeError as e:
        if "Called get_config outside of a runnable context" not in str(e):
            raise

    # 审核（无需再传 messages，保守为 []）
    hr_update, next_node = await maybe_interrupt_review(
        state=state,
        config=config,
        node_name="writer_outline",
        input_messages=[],
        review_file_name=review_file,
        review_content=batch_content,
        next_node_if_pass="writer_outline",
    )
    if hr_update is None:
        hr_update, next_node = {}, "writer_outline"
    update.update(hr_update)

    # 进度统计（用于 CLI 完成行显示 [completed/total]）
    try:
        total = len(state.get("outline_task_expected_indices") or [])
    except Exception:
        total = 0
    try:
        # 已完成以 state 中 outline_task_result_* 的数量为准
        completed = 0
        for k in state.keys():
            if isinstance(k, str) and k.startswith("outline_task_result_"):
                completed += 1
    except Exception:
        completed = 0

    update["resume_info"] = build_resume_info(
        state,
        node="writer_outline",
        next_node=next_node,
        summary=f"大纲：tasks 并发细化 第{current_volume_number}~{max_required_volume}卷 -> {next_node}",
        start_vol=current_volume_number,
        end_vol=max_required_volume,
        completed=completed,
        total=total,
    )
    return Command(update=update, goto=next_node)


__all__ = [
    "writer_outline_refine_one_task",
    "writer_outline_join_node",
    "writer_outline_spawn_tasks",
]
