from typing import Any, Dict, Literal, Optional

from langchain_core.messages import RemoveMessage
from langgraph.config import RunnableConfig
from langgraph.graph.message import REMOVE_ALL_MESSAGES
from langgraph.types import Command
from src.checkpoint import BookSelectionResult, CheckpointManager

from src.nodes.common.book_types import (
    BookDetail,
    BookSceneDesign,
    CharacterDetailCollection,
    CharacterSummaryCollection,
    WriterPlan,
)
from src.state import State


def _get_current_chapter_number(book_detail: Optional[BookDetail]) -> int:
    if book_detail is None or not getattr(book_detail, "chapters", None):
        return 1
    return len(book_detail.chapters) + 1


def _determine_next_node(resume_state: Dict[str, Any], writer_current_plan: WriterPlan) -> str:
    # interrupt-only：清理可能存在的旧字段
    if "human_review_params" in resume_state:
        try:
            resume_state.pop("human_review_params", None)
        except Exception:
            pass

    # 1) 策划案
    if not writer_current_plan.is_all_step_completed():
        return "bs_steps"
    if not writer_current_plan.is_all_step_refined():
        return "bs_refine"

    # 2) 角色
    character_summaries: CharacterSummaryCollection = resume_state.get("character_summaries", None)
    character_details: CharacterDetailCollection = resume_state.get("character_details", None)
    if character_summaries is None or not getattr(character_summaries, "characters", None):
        return "writer_character"
    if character_details is None or len(character_details.characters) < len(character_summaries.characters):
        return "writer_character"

    # 3) 大纲
    book_detail: BookDetail = resume_state.get("book_detail", None)
    if book_detail is None or not getattr(book_detail, "volume_outlines", None):
        return "writer_outline"
    current_chapter_number = _get_current_chapter_number(book_detail)
    if not book_detail.is_outline_ready_for_writing(current_chapter_number):
        return "writer_outline"

    # 4) 场景
    scene_design: BookSceneDesign = resume_state.get("scene_design", None)
    if scene_design is None or not getattr(scene_design, "volume_scenes", None):
        return "writer_scene"
    if not scene_design.is_scene_ready_for_writing(current_chapter_number):
        return "writer_scene"

    # 5) 默认进入流式写作
    return "stream_write"


async def progress_check_fn(state: State, config: RunnableConfig) -> Command[
    Literal[
        "bs_planner",
        "bs_steps",
        "bs_refine",
        "writer_character",
        "writer_outline",
        "writer_scene",
        "stream_write",
    ]
]:
    # 兼容 CLI 已传入的恢复参数：若已指定 thread_id（以及可选的 checkpoint_id），则跳过本地再次扫描
    try:
        configurable = (
            config.get("configurable", {}) if isinstance(config, dict) else getattr(config, "configurable", {})
        ) or {}
        thread_id = configurable.get("thread_id")
        checkpoint_id = configurable.get("checkpoint_id")
        default_new_project = bool(configurable.get("default_new_project", False))

        # 1) CLI 指定了恢复线程：直接按线程/可选 checkpoint 恢复，不再二次扫描
        if thread_id and not default_new_project:
            async with CheckpointManager.connect() as mgr:
                # 通过 checkpointer 直接获取指定线程（与可选 checkpoint）的完整状态
                cp_cfg: Dict[str, Any] = {"configurable": {"thread_id": thread_id}}
                if checkpoint_id:
                    cp_cfg["configurable"]["checkpoint_id"] = checkpoint_id

                checkpoint_data = await mgr.checkpointer.aget(cp_cfg)
                if checkpoint_data is None:
                    print("⚠️ 未找到可用的历史状态，转入新项目流程")
                    return Command(update={"messages": state.get("messages", [])}, goto="bs_planner")

                checkpoint = checkpoint_data.checkpoint if hasattr(checkpoint_data, "checkpoint") else checkpoint_data
                resume_state = checkpoint.get("channel_values", {}) if isinstance(checkpoint, dict) else None
                if not resume_state:
                    print("⚠️ 历史状态为空，转入新项目流程")
                    return Command(update={"messages": state.get("messages", [])}, goto="bs_planner")

                writer_current_plan: WriterPlan = resume_state.get("writer_current_plan")
                if writer_current_plan is None:
                    print("⚠️ 恢复的状态中缺少 writer_current_plan，转入新项目流程")
                    return Command(update={"messages": state.get("messages", [])}, goto="bs_planner")

                next_node = _determine_next_node(resume_state, writer_current_plan)
                return [
                    Command(update={"messages": [RemoveMessage(id=REMOVE_ALL_MESSAGES)]}),
                    Command(update={**resume_state}, goto=next_node),
                ]

        # 2) 未指定线程：进入交互式扫描/选择（仅执行一次）
        print("🔍 正在扫描数据库中的小说项目...")
        async with CheckpointManager.connect() as mgr:
            selection_result: BookSelectionResult = await mgr.prompt_select_book()

            if selection_result.action == "resume":
                # 优先按用户选择的线程/特定 checkpoint 恢复
                if selection_result.thread_id:
                    cp_cfg: Dict[str, Any] = {"configurable": {"thread_id": selection_result.thread_id}}
                    if selection_result.checkpoint_id:
                        cp_cfg["configurable"]["checkpoint_id"] = selection_result.checkpoint_id

                    checkpoint_data = await mgr.checkpointer.aget(cp_cfg)
                    checkpoint = (
                        checkpoint_data.checkpoint
                        if checkpoint_data and hasattr(checkpoint_data, "checkpoint")
                        else checkpoint_data
                    )
                    resume_state = checkpoint.get("channel_values", {}) if isinstance(checkpoint, dict) else None
                else:
                    resume_state = await mgr.get_latest_state(selection_result.book_id)

                if not resume_state:
                    print(
                        f"⚠️ 无法恢复项目 {selection_result.book_id or selection_result.thread_id} 的状态，将创建新项目"
                    )
                    return Command(update={"messages": state.get("messages", [])}, goto="bs_planner")

                writer_current_plan: WriterPlan = resume_state.get("writer_current_plan")
                if writer_current_plan is None:
                    print("⚠️ 恢复的状态中缺少writer_current_plan，将创建新项目")
                    return Command(update={"messages": state.get("messages", [])}, goto="bs_planner")

                next_node = _determine_next_node(resume_state, writer_current_plan)
                return [
                    Command(update={"messages": [RemoveMessage(id=REMOVE_ALL_MESSAGES)]}),
                    Command(update={**resume_state}, goto=next_node),
                ]
            else:
                # 新项目：保留原始消息，进入 planner
                return Command(update={"messages": state.get("messages", [])}, goto="bs_planner")
    except Exception as e:
        print(f"进度检查失败，将从 bs_planner 启动: {e}")
        return Command(goto="bs_planner")


__all__ = ["progress_check_fn"]
