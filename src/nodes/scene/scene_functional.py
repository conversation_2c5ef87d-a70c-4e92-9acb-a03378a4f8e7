from typing import Literal, Optional

from langchain_core.messages import BaseMessage
from langgraph.config import RunnableConfig
from langgraph.types import Command

from src.context.context_builder import ContextBuilder
from src.nodes.common.book_types import (
    BookDetail,
    BookSceneDesign,
    CharacterDetailCollection,
    VolumeSceneDesign,
    WriterPlan,
)
from src.nodes.common.node_kit import build_resume_info, llm_json, maybe_interrupt_review, write_text_under_book
from src.nodes.common.store_manager import Writer<PERSON><PERSON>Manager
from src.nodes.scene.scene_prompt import smart_volume_scene_design_template, volume_scene_design_template
from src.nodes.scene.scene_tasks import writer_scene_design_one_task, writer_scene_join_node, writer_scene_spawn_tasks
from src.state import State


def _get_current_chapter_number(book_detail: Optional[BookDetail]) -> int:
    if book_detail is None or not getattr(book_detail, "chapters", None):
        return 1
    return len(book_detail.chapters) + 1


def _build_scene_messages(
    state: State, config: RunnableConfig, next_volume: int, current_volume_outline_text: str
) -> list[BaseMessage]:
    refined_book_setting = state.get("refined_book_setting")
    character_details: CharacterDetailCollection = state.get("character_details")
    character_content = character_details.get_characters_content() if character_details else ""

    enable_smc = config.get("configurable", {}).get("enable_smart_character_management", True)
    scene_prompt = (smart_volume_scene_design_template if enable_smc else volume_scene_design_template).format(
        volume_number=next_volume,
        refined_book_setting=refined_book_setting,
        character_design=character_content,
        volume_outline=current_volume_outline_text,
    )

    cb = ContextBuilder()
    cb.header()
    cb.section("场景设计任务", scene_prompt)
    cb.json_schema(VolumeSceneDesign.model_json_schema())

    demo_chapter = config.get("configurable", {}).get("demo_chapter")
    cb.demo(demo_chapter)

    budget = config.get("configurable", {}).get("prompt_budget_tokens")
    return cb.build(max_tokens=budget, policy="scene")


async def writer_scene_fn(
    state: State, config: RunnableConfig
) -> Command[
    Literal["stream_write", "writer_scene", "writer_outline", "writer_character", "bs_steps", "bs_refine", "bs_planner"]
]:
    plan: WriterPlan | None = state.get("writer_current_plan")
    book_detail: BookDetail | None = state.get("book_detail")
    book_id = state.get("book_id")
    book_name = state.get("book_name")
    character_summaries = state.get("character_summaries")
    character_details: CharacterDetailCollection | None = state.get("character_details")

    # 前置检查（与旧实现保持一致）
    if not plan or not plan.is_all_step_completed():
        return Command(goto="bs_steps")
    if not plan.is_all_step_refined():
        return Command(goto="bs_refine")
    if book_id is None:
        return Command(goto="bs_planner")
    if character_summaries is None or not getattr(character_summaries, "characters", None):
        return Command(goto="writer_character")
    if character_details is None:
        return Command(goto="writer_character")
    if len(character_details.characters) != len(character_summaries.characters):
        return Command(goto="writer_character")
    if book_detail is None:
        return Command(goto="writer_outline")

    current_chapter = _get_current_chapter_number(book_detail)
    if not book_detail.is_outline_ready_for_writing(current_chapter):
        return Command(goto="writer_outline")

    # 初始化场景设计结构（仅一次）
    if state.get("scene_design") is None:
        volume_scenes = [
            VolumeSceneDesign(volume_number=vo.volume_number, scenes=[], design_completed=False)
            for vo in (book_detail.volume_outlines or [])
        ]
        scene_design = BookSceneDesign(volume_scenes=volume_scenes)
        update = {"scene_design": scene_design}
        update["resume_info"] = build_resume_info(
            state,
            node="writer_scene",
            next_node="writer_scene",
            summary="场景：初始化结构 -> writer_scene",
            start_vol=min([vo.volume_number for vo in (book_detail.volume_outlines or [])] or [1]),
            end_vol=max([vo.volume_number for vo in (book_detail.volume_outlines or [])] or [1]),
        )
        return Command(update=update, goto="writer_scene")

    # 并发细化：从当前卷开始到后续2卷内所有未完成的卷
    scene_design: BookSceneDesign = state.get("scene_design")
    current_volume_number = (current_chapter - 1) // 100 + 1
    max_required_volume = current_volume_number + 2

    targets: list[int] = []
    for vol in book_detail.volume_outlines:
        if current_volume_number <= vol.volume_number <= max_required_volume:
            vs = scene_design.get_volume_scenes(vol.volume_number)
            if not vs or not vs.design_completed:
                targets.append(vol.volume_number)

    if not targets:
        return Command(goto="stream_write")

    def _find_outline(vol_number: int):
        for item in book_detail.volume_outlines:
            if item.volume_number == vol_number:
                return item
        return None

    # 使用 LangGraph task：spawn -> join
    # 附带书写批次范围，供 join 使用
    update = {
        "scene_task_current_volume": current_volume_number,
        "scene_task_max_volume": max_required_volume,
    }
    cmd = await writer_scene_spawn_tasks(state, targets)
    merged_update = {**update, **(cmd.update or {})}
    # 将子命令的 goto（其中包含 Send(...) 序列）与 join 跳转串联
    return Command(update=merged_update, goto=cmd.goto)


__all__ = ["writer_scene_fn"]
