from __future__ import annotations

from typing import Literal, Optional

from langchain_core.messages import BaseMessage
from langgraph.config import RunnableConfig
from langgraph.types import Command, Send

from src.context.context_builder import ContextBuilder
from src.nodes.common.book_types import BookDetail, BookSceneDesign, CharacterDetailCollection, VolumeSceneDesign
from src.nodes.common.node_kit import build_resume_info, llm_json, maybe_interrupt_review, write_text_under_book
from src.nodes.scene.scene_prompt import smart_volume_scene_design_template, volume_scene_design_template
from src.state import State


def _build_scene_messages(
    state: State, config: RunnableConfig, next_volume: int, current_volume_outline_text: str
) -> list[BaseMessage]:
    refined_book_setting = state.get("refined_book_setting")
    character_details: CharacterDetailCollection = state.get("character_details")
    character_content = character_details.get_characters_content() if character_details else ""

    enable_smc = config.get("configurable", {}).get("enable_smart_character_management", True)
    scene_prompt = (smart_volume_scene_design_template if enable_smc else volume_scene_design_template).format(
        volume_number=next_volume,
        refined_book_setting=refined_book_setting,
        character_design=character_content,
        volume_outline=current_volume_outline_text,
    )

    cb = ContextBuilder()
    cb.header()
    cb.section("场景设计任务", scene_prompt)
    cb.json_schema(VolumeSceneDesign.model_json_schema())

    demo_chapter = config.get("configurable", {}).get("demo_chapter")
    cb.demo(demo_chapter)

    budget = config.get("configurable", {}).get("prompt_budget_tokens")
    return cb.build(max_tokens=budget, policy="scene")


async def writer_scene_design_one_task(state: State, config: RunnableConfig) -> Command[Literal["writer_scene_join"]]:
    book_detail: BookDetail | None = state.get("book_detail")
    scene_design: BookSceneDesign = state.get("scene_design")
    vol_number = int(state.get("task_target_volume"))

    # 找卷
    vo = None
    for item in book_detail.volume_outlines or []:
        if item.volume_number == vol_number:
            vo = item
            break
    if vo is None:
        return Command(goto="writer_scene_join")

    msgs = _build_scene_messages(state, config, vol_number, vo.get_completed_contents())
    refined_one: VolumeSceneDesign = await llm_json("writer_scene", msgs, VolumeSceneDesign, config=config)
    if not refined_one.scenes:
        return Command(goto="writer_scene_join")
    refined_one.design_completed = True
    refined_one.volume_number = vol_number

    # 落盘单卷
    book_name = state.get("book_name")
    book_id = state.get("book_id")
    file_rel_one = f"scene/第{vol_number}卷场景清单.md"
    _ = write_text_under_book(book_name, book_id, file_rel_one, refined_one.get_scenes_content())

    key = f"scene_task_result_{vol_number}"
    return Command(update={key: refined_one.model_dump()}, goto="writer_scene_join")


async def writer_scene_spawn_tasks(state: State, targets: list[int]) -> Command[Literal["writer_scene_join"]]:
    tasks = [Send("writer_scene_design_one", {"task_target_volume": v}) for v in targets]
    return Command(update={"scene_task_expected_volumes": targets}, goto=tasks + ["writer_scene_join"])


async def writer_scene_join_node(
    state: State, config: RunnableConfig
) -> Command[Literal["writer_scene", "stream_write"]]:
    scene_design: BookSceneDesign = state.get("scene_design")
    book_detail: BookDetail | None = state.get("book_detail")
    book_id = state.get("book_id")
    book_name = state.get("book_name")

    if scene_design is None or book_detail is None:
        return Command(goto="writer_scene")

    # 合并结果
    all_msgs: list[BaseMessage] = []
    batch_contents: list[str] = []
    last_vol: int | None = None

    for k, v in list(state.items()):
        if isinstance(k, str) and k.startswith("scene_task_result_") and isinstance(v, dict):
            try:
                vol = int(k.split("_")[-1])
                refined = VolumeSceneDesign.model_validate(v)
            except Exception:
                continue
            exist = scene_design.get_volume_scenes(vol)
            if exist:
                exist.scenes = refined.scenes
                exist.design_completed = True
            else:
                scene_design.volume_scenes.append(refined)
            last_vol = vol
            batch_contents.append(refined.get_scenes_content())

    # 若任务未全部完成，则继续等待
    expected = set(state.get("scene_task_expected_volumes") or [])
    have = set()
    for k in state.keys():
        if isinstance(k, str) and k.startswith("scene_task_result_"):
            try:
                have.add(int(k.split("_")[-1]))
            except Exception:
                pass
    if expected and not expected.issubset(have):
        return Command(goto="writer_scene_join")

    # 入库（一次）
    try:
        if book_id:
            from src.nodes.common.store_manager import WriterStoreManager

            sm = WriterStoreManager(book_id=book_id)
            await sm.store_scene_design(scene_design)
    except RuntimeError as e:
        if "Called get_config outside of a runnable context" not in str(e):
            raise

    # 批量文件
    # 尝试从 state 获取范围信息（如无，则估算）
    current_volume_number = state.get("scene_task_current_volume", 1)
    max_required_volume = state.get("scene_task_max_volume", current_volume_number)
    batch_file_rel = f"scene/批量-第{current_volume_number}~{max_required_volume}卷-场景设计.md"
    seg_lines = ["# 场景设计批量更新", "", f"范围：第{current_volume_number}~{max_required_volume}卷", "", "---", ""]
    # 以卷号排序写入
    try:
        parsed = sorted(
            [
                (int(k.split("_")[-1]), VolumeSceneDesign.model_validate(v))
                for k, v in state.items()
                if isinstance(k, str) and k.startswith("scene_task_result_") and isinstance(v, dict)
            ],
            key=lambda x: x[0],
        )
    except Exception:
        parsed = []
    for vol_number, refined in parsed:
        seg_lines.append(f"## 第{vol_number}卷 场景清单")
        seg_lines.append("")
        seg_lines.append(refined.get_scenes_content())
        seg_lines.append("")
    batch_content = "\n".join(seg_lines)
    review_file = write_text_under_book(book_name, book_id, batch_file_rel, batch_content)

    update = {"scene_design": scene_design, "scene_last_volume": last_vol}

    hr_update, next_node = await maybe_interrupt_review(
        state=state,
        config=config,
        node_name="writer_scene",
        input_messages=all_msgs,
        review_file_name=review_file,
        review_content=batch_content,
        next_node_if_pass="writer_scene",
    )
    if hr_update is None:
        hr_update, next_node = {}, "writer_scene"
    update.update(hr_update)

    # 进度统计（用于 CLI 完成行显示 [completed/total]）
    try:
        total = len(state.get("scene_task_expected_volumes") or [])
    except Exception:
        total = 0
    try:
        completed = 0
        for k in state.keys():
            if isinstance(k, str) and k.startswith("scene_task_result_"):
                completed += 1
    except Exception:
        completed = 0

    update["resume_info"] = build_resume_info(
        state,
        node="writer_scene",
        next_node=next_node,
        summary=f"场景：tasks 并发设计 第{current_volume_number}~{max_required_volume}卷 -> {next_node}",
        start_vol=current_volume_number,
        end_vol=max_required_volume,
        completed=completed,
        total=total,
    )
    return Command(update=update, goto=next_node)


__all__ = [
    "writer_scene_design_one_task",
    "writer_scene_join_node",
    "writer_scene_spawn_tasks",
]
