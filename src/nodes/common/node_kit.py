import logging
import re
from pathlib import Path
from typing import Any, Dict, List, Literal, NotRequired, Optional, Tu<PERSON>, TypedDict

from langchain_core.messages import BaseMessage, HumanMessage
from langgraph.types import interrupt
from src.common.llm_config import ModelTier
from src.common.utils.filesystem_utils import FileSystemUtils

from src.common.utils.llm_request_utils import llm_str_request, llm_structured_request
from src.common.utils.time_utils import get_db_timestamp
from src.nodes.common.book_types import Chapter
from src.nodes.common.store_manager import WriterStoreManager


logger = logging.getLogger(__name__)


# -------------------- 人审协议类型与通用工具 --------------------


class HumanReviewContent(TypedDict, total=False):
    node: str
    file: str
    content: str
    tips: str


class HumanReviewResult(TypedDict, total=False):
    action: Literal["pass", "skip", "feedback"]
    text: NotRequired[str]
    tier: NotRequired[Literal["high", "medium", "low"]]
    domain: NotRequired[str]
    count: NotRequired[int]


# 统一域名规范映射（用于 /skip 策略与统计口径）
REVIEW_DOMAIN_ALIAS: Dict[str, str] = {
    "outline": "outline",
    "scene": "scene",
    "stream": "stream",
    "stream_write": "stream",
    "steps": "steps",
    "refine": "refine",
    "planner": "planner",
    "character": "character",
    "chapter": "chapter",
}


def normalize_review_domain(domain: Optional[str]) -> Optional[str]:
    if not domain:
        return None
    try:
        return REVIEW_DOMAIN_ALIAS.get(domain.lower(), domain.lower())
    except Exception:
        return domain


def parse_human_review_input(user_input: str) -> HumanReviewResult:
    """
    解析终端/客户端输入为统一的人审协议：
    - 空字符串或 '/pass' => {action: 'pass'}
    - '/skip [domain] [n]' => {action: 'skip', domain?, count?}
    - '/high|/medium|/low [意见]' => {action: 'feedback', tier, text?}
    - 其他文本 => {action: 'feedback', text}
    """
    raw = (user_input or "").strip()
    if raw == "" or raw.lower() == "/pass":
        return {"action": "pass"}

    lowered = raw.lower()
    for prefix, tier in ("/high", "high"), ("/medium", "medium"), ("/low", "low"):
        if lowered.startswith(prefix + " ") or lowered == prefix:
            text = raw[len(prefix) :].strip()
            if text.startswith(" "):
                text = text.strip()
            resp: HumanReviewResult = {"action": "feedback", "tier": tier}  # type: ignore[assignment]
            if text:
                resp["text"] = text
            return resp

    if lowered.startswith("/skip"):
        m = re.match(r"^/skip(?:\s+(\w+))?(?:\s+(\d+))?$", lowered)
        if m:
            domain = m.group(1)
            count = int(m.group(2)) if m.group(2) else 1
            resp2: HumanReviewResult = {"action": "skip"}
            if domain:
                resp2["domain"] = domain
                resp2["count"] = count
            return resp2
        return {"action": "skip"}

    return {"action": "feedback", "text": raw}


def apply_skip_policy(config: dict, domain: Optional[str], count: int) -> None:
    """
    将 /skip 指令合并进 config.configurable.skip_policies。
    - 域名做 normalize（保持与 node_kit.maybe_interrupt_review 的统计口径一致）
    - count 必为正整数，非法时忽略
    """
    try:
        if not domain:
            return
        dom = normalize_review_domain(domain)
        if not dom:
            return
        try:
            c = int(count)
        except Exception:
            return
        if c <= 0:
            return
        conf = config.setdefault("configurable", {})
        policies = conf.setdefault("skip_policies", {})
        policies[dom] = int(c)
    except Exception:
        # 非关键路径，静默失败
        pass


def _get_configurable(config: dict) -> dict:
    """稳健获取 RunnableConfig.configurable。

    兼容：
    - 原始 dict: {"configurable": {...}}
    - Mapping 风格（如 RunnableConfig）: obj.get("configurable", {})
    - 属性风格: obj.configurable
    任何异常均返回空 dict。
    """
    try:
        # 1) 普通 dict
        if isinstance(config, dict):
            return config.get("configurable", {}) or {}
        # 2) Mapping.get 接口
        get_attr = getattr(config, "get", None)
        if callable(get_attr):
            try:
                cfg = config.get("configurable", {})  # type: ignore[attr-defined]
            except Exception:
                cfg = {}
            return (cfg or {}) if isinstance(cfg, dict) else (dict(cfg) if cfg else {})
        # 3) 属性访问
        cfg = getattr(config, "configurable", None)
        return (cfg or {}) if isinstance(cfg, dict) else (dict(cfg) if cfg else {})
    except Exception:
        return {}


def tier_of(config: dict) -> dict:
    cfg = _get_configurable(config)
    return {
        "tier": cfg.get("model_tier"),
        "llm_params": cfg.get("llm_params", {}),
    }


async def llm_json(
    node_name: str,
    messages: List[BaseMessage],
    schema_type: type,
    *,
    config: dict,
    stage_name: Optional[str] = None,
    tier_override: Optional[str] = None,
    reject_schema_echo: bool = True,
) -> Any:
    params = tier_of(config)
    # 支持 tier_override，并将字符串转换为 ModelTier
    tier_value = params.get("tier")
    if tier_override is not None:
        tier_value = tier_override
    if isinstance(tier_value, str):
        lowered = tier_value.lower().strip()
        if lowered in ("low", "medium", "high"):
            tier_value = ModelTier[lowered.upper()]

    response_raw, response_parsed, response_error = await llm_structured_request(
        node_name=node_name,
        input_messages=messages,
        schema_type=schema_type,
        stage_name=stage_name,
        tier=tier_value,
        llm_params=params.get("llm_params", {}),
    )
    if response_error is not None or response_parsed is None:
        raise ValueError(f"{node_name} 结构化响应解析失败: {response_error}")
    # 结构化稳健性：检测模型回显 JSON Schema 或示例内容
    try:
        if reject_schema_echo and _has_schema_echo(response_parsed):
            logger.warning(f"{node_name} 检测到疑似 Schema 回显，尝试一次回退重试")
            retry_messages = list(messages)
            retry_messages.append(
                HumanMessage(
                    name="review",
                    content="请不要输出JSON Schema或示例，只输出符合给定Schema的实例数据（纯内容）。",
                )
            )
            response_raw2, response_parsed2, response_error2 = await llm_structured_request(
                node_name=node_name,
                input_messages=retry_messages,
                schema_type=schema_type,
                stage_name=(f"{stage_name}-retry" if stage_name else None),
                tier=tier_value,
                llm_params=params.get("llm_params", {}),
            )
            if response_error2 is not None or response_parsed2 is None:
                raise ValueError(f"{node_name} 回退重试解析失败: {response_error2}")
            if _has_schema_echo(response_parsed2):
                raise ValueError(f"{node_name} 结构化响应疑似回显Schema（两次），已拒绝")
            return response_parsed2
    except Exception:
        # 向上抛错由调用方处理
        raise
    return response_parsed


async def llm_text(
    node_name: str,
    messages: List[BaseMessage],
    *,
    config: dict,
    stage_name: Optional[str] = None,
    tier_override: Optional[str] = None,
) -> Tuple[str, str]:
    params = tier_of(config)
    tier_value = params.get("tier")
    if tier_override is not None:
        tier_value = tier_override
    if isinstance(tier_value, str):
        lowered = tier_value.lower().strip()
        if lowered in ("low", "medium", "high"):
            tier_value = ModelTier[lowered.upper()]

    # 首次请求
    content, cot = await llm_str_request(
        node_name=node_name,
        input_messages=messages,
        stage_name=stage_name,
        tier=tier_value,
        llm_params=params.get("llm_params", {}),
    )

    # 稳健性校验（可选）：最小长度与敏感模式检测
    try:
        cfg = _get_configurable(config)
        min_len = int(cfg.get("llm_text_min_length", 0) or 0)
        patterns = cfg.get("llm_text_sensitive_patterns") or []
        if isinstance(patterns, str):
            patterns = [patterns]

        def _violations(text: str) -> List[str]:
            problems: List[str] = []
            try:
                if min_len > 0:
                    if len((text or "").strip()) < min_len:
                        problems.append(f"too_short(<{min_len})")
                if patterns:
                    for pat in patterns:
                        try:
                            if re.search(pat, text or ""):
                                problems.append(f"sensitive:{pat}")
                        except Exception:
                            # 忽略非法正则
                            pass
            except Exception:
                return problems
            return problems

        problems_first = _violations(content)
        if problems_first:
            logger.warning(f"{node_name} 文本校验未通过：{problems_first}，尝试一次回退重试")
            # 追加一条 review 提示并重试（可选升级 tier）
            retry_msgs = list(messages)
            tips: List[str] = []
            if min_len > 0:
                tips.append(f"不少于{min_len}字")
            if patterns:
                tips.append("避免触发敏感模式")
            tip_text = "；".join(tips) if tips else "请根据要求重写"
            retry_msgs.append(HumanMessage(name="review", content=f"{tip_text}。请优化并重写。"))

            retry_tier = cfg.get("llm_text_retry_tier")
            if isinstance(retry_tier, str) and retry_tier.lower().strip() in ("low", "medium", "high"):
                retry_tier_value = ModelTier[retry_tier.upper()]  # type: ignore
            else:
                retry_tier_value = tier_value

            content2, cot2 = await llm_str_request(
                node_name=node_name,
                input_messages=retry_msgs,
                stage_name=(f"{stage_name}-retry" if stage_name else "retry"),
                tier=retry_tier_value,
                llm_params=params.get("llm_params", {}),
            )

            problems_second = _violations(content2)
            if problems_second:
                raise ValueError(f"{node_name} 文本响应未通过稳健性校验（两次）：{problems_second}")
            return content2, cot2
    except Exception:
        # 若校验或重试逻辑本身异常，向上抛出以由上层决定（通常进入人审/重试策略）
        raise

    return content, cot


def write_text_under_book(book_name: str, book_id: str, relative_path: str, content: str) -> str:
    """
    将内容写入 workspace 下的 `book_name/book_id/relative_path`。
    返回用于审核展示的相对路径。
    """
    full_path = f"{book_name}/{book_id}/{relative_path}"
    try:
        FileSystemUtils.write_content_to_workspace(full_path, content)
        logger.info(f"保存文件: {full_path}")
    except RuntimeError as e:
        if "Called get_config outside of a runnable context" in str(e):
            logger.warning(f"不在运行上下文，写文件被跳过: {full_path}")
        else:
            raise
    return full_path


def write_batch_under_book(
    book_name: str,
    book_id: str,
    files: List[Tuple[str, str]],
    mode: str = "w",
) -> List[str]:
    """
    批量写入文件到 workspace 下的 `book_name/book_id/` 目录。
    files: 列表[(相对路径, 内容)]。
    返回写入的相对路径列表。
    """
    written: List[str] = []
    for relative_path, content in files:
        full_path = f"{book_name}/{book_id}/{relative_path}"
        try:
            FileSystemUtils.write_content_to_workspace(full_path, content, mode=mode)
            written.append(full_path)
            logger.info(f"保存文件: {full_path}")
        except RuntimeError as e:
            if "Called get_config outside of a runnable context" in str(e):
                logger.warning(f"不在运行上下文，写文件被跳过: {full_path}")
            else:
                raise
    return written


def rel_path_for_chapter(chapter_number: int, chapter_title: str) -> str:
    """标准化章节文件相对路径：chapter/第N章-标题.md"""
    safe_title = str(chapter_title).replace("/", "-").replace(" ", "")
    return f"chapter/第{chapter_number}章-{safe_title}.md"


async def save_plan_to_db(book_id: Optional[str], plan: Any) -> None:
    if not book_id:
        return
    manager = WriterStoreManager(book_id=book_id)
    await manager.store_writer_plan(plan=plan)


async def maybe_interrupt_review(
    *,
    state: dict,
    config: dict,
    node_name: str,
    input_messages: list[BaseMessage],
    review_file_name: str,
    review_content: str,
    next_node_if_pass: str,
) -> Tuple[Optional[dict], Optional[str]]:
    """
    使用 LangGraph interrupt 的人审闸门。
    - 返回 (state_update, next_node)。若返回 (None, None) 表示未启用或未触发。
    - 需上层 runner 支持 interrupt 交互；默认仅在 `configurable.enable_interrupt_review=True` 时启用。
    """
    cfg = _get_configurable(config)
    if not cfg.get("enable_interrupt_review", False):
        return None, None

    # 统一 domain 映射（用于 /skip 策略）
    node_to_domain: Dict[str, str] = {
        "writer_outline": "outline",
        "writer_scene": "scene",
        "stream_write": "stream",
        "bs_planner": "planner",
        "bs_steps": "steps",
        "bs_refine": "refine",
        "writer_character": "character",
        "dynamic_character": "character",
        "chapter_segment": "chapter",
    }
    domain = node_to_domain.get(node_name, node_name)

    # 读取/初始化跳过计数器（优先 state 可持久化，其次 config 指令）
    skip_counters: Dict[str, int] = dict(state.get("skip_counters") or {})
    if domain not in skip_counters:
        policies: Dict[str, int] = dict(cfg.get("skip_policies", {}) or {})
        if domain in policies and isinstance(policies[domain], int) and policies[domain] > 0:
            skip_counters[domain] = int(policies[domain])

    # 若存在跳过计数器，则本次直接通过，并将计数器-1
    if isinstance(skip_counters.get(domain), int) and skip_counters[domain] > 0:
        new_count = max(0, int(skip_counters[domain]) - 1)
        skip_counters[domain] = new_count
        update_pass = {
            "skip_counters": skip_counters,
        }
        # 同步追加审阅记录（以“系统自动跳过”标注）
        try:
            dir_path = review_file_name.rsplit("/", 1)[0] if "/" in review_file_name else ""
            review_log_rel = f"{dir_path}/审阅记录.md" if dir_path else "审阅记录.md"
            ts = get_db_timestamp()
            auto_lines = [
                f"\n\n## 审阅跳过（{ts}）",
                f"- 节点: {node_name}",
                f"- 域: {domain}",
                f"- 文件: {review_file_name}",
                f"- 操作: 系统根据 /skip 策略自动通过（剩余 {new_count} 次）",
                "\n---\n",
            ]
            FileSystemUtils.write_content_to_workspace(review_log_rel, "\n".join(auto_lines), mode="a")
            _update_review_index_for_dir(dir_path)
        except Exception:
            pass
        return update_pass, next_node_if_pass

    # 触发中断，等待外部反馈
    feedback = interrupt(
        {
            "node": node_name,
            "file": review_file_name,
            "content": review_content,
            "tips": "输入 /pass 通过；或 /skip 跳过；或输入意见文本；或 /high|/medium|/low 可指定等级。",
        }
    )

    # 兼容简单协议：{'action': 'pass'} / {'action': 'skip'} / {'action': 'feedback', 'text': '...','tier': 'high|medium|low'}
    if isinstance(feedback, dict) and feedback.get("action") in {"pass", "skip"}:
        # 直接通过（若为 skip，不在此处改变计数，计数在进入前已处理）
        # 写入审阅记录元数据
        try:
            dir_path = review_file_name.rsplit("/", 1)[0] if "/" in review_file_name else ""
            review_log_rel = f"{dir_path}/审阅记录.md" if dir_path else "审阅记录.md"
            ts = get_db_timestamp()
            meta_lines = [
                f"\n\n## 审阅通过（{ts}）",
                f"- 节点: {node_name}",
                f"- 域: {domain}",
                f"- 文件: {review_file_name}",
                f"- 操作: {feedback.get('action')}",
                "\n---\n",
            ]
            FileSystemUtils.write_content_to_workspace(review_log_rel, "\n".join(meta_lines), mode="a")
            _update_review_index_for_dir(dir_path)
        except Exception:
            pass
        return {}, next_node_if_pass

    # 反馈重生（一次）：将意见拼接为 HumanMessage 并重新生成文本/结构（此处仅支持文本）
    fb_text = None
    tier = None
    if isinstance(feedback, dict):
        fb_text = feedback.get("text")
        tier = feedback.get("tier")
    if not fb_text and isinstance(feedback, str):
        fb_text = feedback

    if fb_text or tier:
        # 追加一条 review 消息
        messages = list(input_messages)
        if fb_text:
            messages.append(HumanMessage(name="review", content=fb_text))
        content, _ = await llm_text(node_name, messages, config=config, tier_override=tier)

        # 写入文件（若为 stream_write 直接覆盖缓冲；否则替换 review 内容）
        try:
            FileSystemUtils.write_content_to_workspace(review_file_name, content)
        except Exception:
            pass

        # 多轮意见汇总到同目录的审阅记录文件（append）
        try:
            # 目录 + 审阅记录（增强元数据）
            dir_path = review_file_name.rsplit("/", 1)[0] if "/" in review_file_name else ""
            review_log_rel = f"{dir_path}/审阅记录.md" if dir_path else "审阅记录.md"
            ts = get_db_timestamp()
            # 尝试解析卷号或批量范围
            volume_info = "-"
            try:
                m1 = re.search(r"第(\d+)卷", review_file_name)
                m2 = re.search(r"第(\d+)~(\d+)卷", review_file_name)
                if m2:
                    volume_info = f"第{m2.group(1)}~{m2.group(2)}卷"
                elif m1:
                    volume_info = f"第{m1.group(1)}卷"
            except Exception:
                pass
            note_lines = [
                f"\n\n## 审阅意见（{ts}）",
                f"- 节点: {node_name}",
                f"- 域: {domain}",
                f"- 文件: {review_file_name}",
                f"- 卷信息: {volume_info}",
                f"- 等级: {tier or '-'}",
                "",
                (fb_text or "(无正文意见)"),
                "\n---\n",
            ]
            FileSystemUtils.write_content_to_workspace(review_log_rel, "\n".join(note_lines), mode="a")
            # 批量与单卷解耦记录
            try:
                if "批量-" in review_file_name:
                    batch_log_rel = f"{dir_path}/批量-审阅记录.md" if dir_path else "批量-审阅记录.md"
                    FileSystemUtils.write_content_to_workspace(batch_log_rel, "\n".join(note_lines), mode="a")
                else:
                    per_log_rel = f"{dir_path}/单卷-审阅记录.md" if dir_path else "单卷-审阅记录.md"
                    FileSystemUtils.write_content_to_workspace(per_log_rel, "\n".join(note_lines), mode="a")
            except Exception:
                pass
            # 索引更新
            _update_review_index_for_dir(dir_path)
        except Exception:
            pass

        # 停留在当前节点，继续人工审核循环
        return {}, node_name

    # 未识别的反馈：回落为通过（记录一次“默认通过”）
    try:
        dir_path = review_file_name.rsplit("/", 1)[0] if "/" in review_file_name else ""
        review_log_rel = f"{dir_path}/审阅记录.md" if dir_path else "审阅记录.md"
        ts = get_db_timestamp()
        meta_lines = [
            f"\n\n## 审阅默认通过（{ts}）",
            f"- 节点: {node_name}",
            f"- 域: {domain}",
            f"- 文件: {review_file_name}",
            f"- 操作: 默认通过（未识别反馈）",
            "\n---\n",
        ]
        FileSystemUtils.write_content_to_workspace(review_log_rel, "\n".join(meta_lines), mode="a")
        _update_review_index_for_dir(dir_path)
    except Exception:
        pass
    return {}, next_node_if_pass


async def save_chapter_to_db(book_id: Optional[str], chapter: Chapter) -> None:
    """章节入库：全文 + 分块向量化。"""
    if not book_id or chapter is None:
        return
    manager = WriterStoreManager(book_id=book_id)
    try:
        await manager.store_chapter(chapter)
    except Exception as e:
        logger.warning(f"章节入库失败: {e}")


# -------------------- 审阅记录索引维护 --------------------


def _update_review_index_for_dir(dir_path: str) -> None:
    """
    为指定目录维护一份 `审阅记录-索引.md`，聚合当前目录内的 `审阅记录*.md` 条目。
    - 解析标题（## 审阅意见/通过/跳过/默认通过）中的时间戳
    - 抽取元数据行（节点/域/文件/卷信息/等级/操作）
    - 生成按时间倒序的索引列表，并链接到对应文件
    """
    try:
        base = FileSystemUtils.get_workspace_path()
        target_dir = (base / dir_path) if dir_path else base
        if not target_dir.exists() or not target_dir.is_dir():
            return

        # 收集候选日志文件
        candidates: list[Path] = []
        for name in ("审阅记录.md", "批量-审阅记录.md", "单卷-审阅记录.md"):
            p = target_dir / name
            if p.exists() and p.is_file():
                candidates.append(p)
        if not candidates:
            return

        entries: list[dict] = []
        heading_pat = re.compile(r"^##\s*(审阅意见|审阅通过|审阅跳过|审阅默认通过)（(.+?)）\s*$")
        meta_pat = re.compile(r"^-\s*(节点|域|文件|卷信息|等级|操作):\s*(.*)\s*$")

        for file_path in candidates:
            try:
                content = file_path.read_text(encoding="utf-8", errors="ignore")
            except Exception:
                continue
            lines = content.splitlines()
            i = 0
            while i < len(lines):
                m = heading_pat.match(lines[i].strip())
                if not m:
                    i += 1
                    continue
                kind = m.group(1)
                ts = m.group(2)
                meta = {
                    "种类": kind,
                    "时间": ts,
                    "文件": None,
                    "节点": None,
                    "域": None,
                    "卷信息": None,
                    "等级": None,
                    "操作": None,
                }
                j = i + 1
                # 读取随后的若干行元数据
                while j < len(lines) and lines[j].strip().startswith("-"):
                    m2 = meta_pat.match(lines[j].strip())
                    if m2:
                        meta[m2.group(1)] = m2.group(2)
                    j += 1
                # 记录条目
                meta["来源日志"] = file_path.name
                entries.append(meta)
                i = j

        if not entries:
            return

        # 时间倒序（字符串对齐：假设 ts 已是可比较的短格式）
        try:
            entries.sort(key=lambda x: x.get("时间") or "", reverse=True)
        except Exception:
            pass

        # 生成索引内容
        lines_out: list[str] = [
            "# 审阅记录索引",
            "",
            f"目录：{dir_path or '.'}",
            "",
            "| 时间 | 节点 | 域 | 卷信息 | 等级/操作 | 文件 | 来源日志 |",
            "|---|---|---|---|---|---|---|",
        ]
        for e in entries:
            t = e.get("时间") or "-"
            node = e.get("节点") or "-"
            dom = e.get("域") or "-"
            vol = e.get("卷信息") or "-"
            lev = e.get("等级") or e.get("操作") or "-"
            file_rel = e.get("文件") or "-"
            src = e.get("来源日志") or "-"
            # 链接到相对文件（若可用）
            file_link = f"[{file_rel}](./{file_rel})" if file_rel != "-" else "-"
            lines_out.append(f"| {t} | {node} | {dom} | {vol} | {lev} | {file_link} | {src} |")

        index_path_rel = f"{dir_path}/审阅记录-索引.md" if dir_path else "审阅记录-索引.md"
        FileSystemUtils.write_content_to_workspace(index_path_rel, "\n".join(lines_out), mode="w")
    except Exception:
        # 索引更新失败不影响主流程
        pass


# -------------------- ResumeInfo 统一构造器 --------------------


def build_resume_info(
    state: dict,
    *,
    node: str,
    next_node: str,
    summary: Optional[str] = None,
    **extra_fields: Any,
) -> list[dict]:
    """构造并返回新的 resume_info 列表（不原地修改 state）。"""
    old_list = state.get("resume_info") or []
    new_list = list(old_list)
    info: Dict[str, Any] = {
        "node": node,
        "next": next_node,
        "book_id": state.get("book_id"),
        "book_name": state.get("book_name"),
    }
    if summary:
        info["summary"] = summary
    for k, v in (extra_fields or {}).items():
        if v is not None:
            info[k] = v
    new_list.append(info)
    return new_list


def build_stream_resume_info(
    state: dict,
    *,
    node: str,
    next_node: str,
    buffer_len: int,
    chunks_count: Optional[int] = None,
    summary: Optional[str] = None,
    **extra_fields: Any,
) -> list[dict]:
    """
    流式写作/后处理相关的 ResumeInfo 构造器：
    - 默认摘要：
      - 若提供 chunks_count："流式写作：缓冲{buffer_len}字 → 后处理{chunks_count}块"
      - 否则："流式写作：缓冲{buffer_len}字 -> {next_node}"
    - 始终写入 buffer_len 字段；可追加自定义字段（如 post_batch_id）。
    """
    if summary is None:
        if chunks_count is not None:
            summary = f"流式写作：缓冲{buffer_len}字 → 后处理{chunks_count}块"
        else:
            summary = f"流式写作：缓冲{buffer_len}字 -> {next_node}"
    fields = dict(extra_fields or {})
    fields.setdefault("buffer_len", buffer_len)
    # 复用 build_resume_info 逻辑
    old_list = state.get("resume_info") or []
    new_list = list(old_list)
    info: Dict[str, Any] = {
        "node": node,
        "next": next_node,
        "book_id": state.get("book_id"),
        "book_name": state.get("book_name"),
        "buffer_len": buffer_len,
    }
    if summary:
        info["summary"] = summary
    for k, v in fields.items():
        if v is not None:
            info[k] = v
    new_list.append(info)
    return new_list


# -------------------- LLM 稳健性：Schema 回显检测 --------------------


def _string_looks_like_json_schema(text: str) -> bool:
    try:
        s = text.strip().lower()
    except Exception:
        return False
    if len(s) < 20:
        return False
    # 常见 JSON Schema 关键词组合
    tokens = [
        '"type"',
        '"properties"',
        '"required"',
        '"$schema"',
        '"definitions"',
        '"oneof"',
        '"anyof"',
        '"allof"',
        "json schema",
        "model_json_schema",
    ]
    score = sum(1 for t in tokens if t in s)
    return score >= 2


def _iter_strings_from_obj(obj: Any):  # type: ignore
    if isinstance(obj, str):
        yield obj
        return
    if isinstance(obj, dict):
        for v in obj.values():
            yield from _iter_strings_from_obj(v)
        return
    if isinstance(obj, (list, tuple, set)):
        for v in obj:
            yield from _iter_strings_from_obj(v)
        return
    # pydantic BaseModel 支持
    try:
        if hasattr(obj, "model_dump") and callable(getattr(obj, "model_dump")):
            yield from _iter_strings_from_obj(obj.model_dump())
            return
    except Exception:
        pass
    # 兜底：不展开任意对象
    return


def _has_schema_echo(obj: Any) -> bool:  # type: ignore
    try:
        for s in _iter_strings_from_obj(obj):
            if isinstance(s, str) and _string_looks_like_json_schema(s):
                return True
        return False
    except Exception:
        return False
