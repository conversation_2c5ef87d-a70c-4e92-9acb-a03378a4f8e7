import base64
import json
import logging
import lzma
from datetime import datetime
from typing import List, Optional, Tuple, Union
from uuid import uuid4

from langchain_core.load.dump import dumps
from langchain_core.load.load import loads

from langgraph.config import get_store
from langgraph.store.postgres.aio import AsyncPostgresStore
from pydantic import BaseModel
from src.common.utils.time_utils import get_db_timestamp
from src.memory.store_utils import *
from src.nodes.common.book_types import BookDetail, Chapter, WriterPlan, WriterPlanStep
from src.state import State


logger = logging.getLogger(__name__)


class WriterStoreManager:
    """
    Writer团队专用的存储管理器 - 统一的数据持久化解决方案

    设计目的：
    - 提供统一的数据存储接口，隐藏底层存储细节
    - 支持多种数据类型的存储：计划、设定、章节、大纲、场景等
    - 实现分块存储和向量搜索功能
    - 使用命名空间模式组织不同类型的数据

    存储架构：
    - 基于LangGraph的AsyncPostgresStore
    - 使用分层命名空间：("writer_app_v2", book_id, data_type)
    - 支持完整存储和分块存储两种模式
    - 集成向量搜索功能

    数据类型支持：
    - plan: 书籍创作计划 (WriterPlan)
    - settings: 书籍设定内容 (步骤执行结果)
    - chapters: 章节内容 (Chapter)
    - outline: 书籍大纲 (BookDetail)
    - scene: 场景设计 (BookSceneDesign)

    使用模式：
    1. 创建实例：WriterStoreManager(book_id="xxx")
    2. 存储数据：await store_manager.store_xxx(data)
    3. 检索数据：data = await store_manager.get_xxx()
    4. 搜索内容：results = await store_manager.search_settings(query)

    扩展指南：
    - 新增数据类型时添加对应的namespace、key生成方法
    - 实现对应的store_xxx和get_xxx方法
    - 考虑是否需要分块存储和向量搜索支持
    """

    def __init__(self, book_id: str, store: Optional[AsyncPostgresStore] = None):
        self.book_id = book_id
        if store is not None:
            self.store = store
        else:
            try:
                self.store = get_store()
            except Exception as e:
                logger.warning(f"无法获取store配置，使用None: {e}")
                self.store = None

    def get_plan_namespace(self) -> Tuple[str, ...]:
        """获取策划案的命名空间（v2）"""
        return ("writer_app_v2", self.book_id, "plan")

    def get_settings_namespace(self) -> Tuple[str, ...]:
        """获取设定内容的命名空间（v2）"""
        return ("writer_app_v2", self.book_id, "settings")

    def get_chapters_namespace(self) -> Tuple[str, ...]:
        """获取章节内容的命名空间（v2）"""
        return ("writer_app_v2", self.book_id, "chapters")

    def get_outline_namespace(self) -> Tuple[str, ...]:
        """获取大纲的命名空间（v2）"""
        return ("writer_app_v2", self.book_id, "outline")

    def get_stream_namespace(self) -> Tuple[str, ...]:
        """获取流式写作分块的命名空间（v2）"""
        return ("writer_app_v2", self.book_id, "stream")

    def get_plan_key(self) -> str:
        """生成策划案key"""
        return f"master_plan"

    def get_refined_book_setting_key(self) -> str:
        return f"refined_book_setting"

    def get_book_detail_key(self) -> str:
        """生成书籍详情key"""
        return f"book_detail"

    def get_step_base_key(self, step_title: str) -> str:
        """生成步骤的基础key"""
        return f"{step_title}"

    def get_step_full_key(self, step_title: str) -> str:
        """生成步骤完整内容的key"""
        base_key = self.get_step_base_key(step_title)
        return f"{base_key}_full"

    def get_volume_outline_key(self) -> str:
        """生成卷大纲的key"""
        return f"volume_outline"

    def get_scene_design_key(self) -> str:
        """生成场景设计的key"""
        return f"scene_design"

    def get_scene_namespace(self) -> Tuple[str, ...]:
        """获取场景设计的命名空间（v2）"""
        return ("writer_app_v2", self.book_id, "scene")

    def get_step_chunk_key(self, step_title: str, chunk_index: int) -> str:
        """生成步骤分块的key"""
        base_key = self.get_step_base_key(step_title)
        return f"{base_key}_chunk_{chunk_index}"

    def get_chapter_base_key(self, chapter_title: str, chapter_number: int = None) -> str:
        """生成章节的基础key"""
        if chapter_number is not None:
            return f"chapter_{chapter_number}_{chapter_title}"
        else:
            return f"chapter_{chapter_title}"

    def get_chapter_full_key(self, chapter_title: str, chapter_number: int = None) -> str:
        """生成章节完整内容的key"""
        base_key = self.get_chapter_base_key(chapter_title, chapter_number)
        return f"{base_key}_full"

    def get_chapter_chunk_key(self, chapter_title: str, chapter_number: int = None, chunk_index: int = 0) -> str:
        """生成章节分块的key"""
        base_key = self.get_chapter_base_key(chapter_title, chapter_number)
        return f"{base_key}_chunk_{chunk_index}"

    async def store_chapter(self, chapter: Chapter) -> list[str]:
        """存储章节全文 + 分块向量化。"""
        namespace = self.get_chapters_namespace()
        base_key = self.get_chapter_base_key(chapter.chapter_title, chapter.chapter_number)
        full_key = self.get_chapter_full_key(chapter.chapter_title, chapter.chapter_number)

        base_metadata = {
            "book_id": self.book_id,
            "chapter_number": chapter.chapter_number,
            "chapter_title": chapter.chapter_title,
            "content_type": "chapter",
            "created_at": get_db_timestamp(),
            "schema_version": 2,
        }

        # 存全文
        await store_aput(
            store=self.store,
            namespace=namespace,
            key=full_key,
            content=chapter.get_completed_content(),
            metadata={**base_metadata, "chunk_type": "full"},
            index=[],
            retries=2,
            retry_backoff_s=0.5,
        )

        # 分块
        docs = store_split_content_to_docs(long_content=chapter.chapter_content, metadata=base_metadata)
        doc_ids: list[str] = []
        for i, doc in enumerate(docs):
            doc_id = self.get_chapter_chunk_key(chapter.chapter_title, chapter.chapter_number, i)
            doc_ids.append(doc_id)
            chunk_metadata = {
                **base_metadata,
                "chunk_type": "partial",
                "chunk_index": i,
                "total_chunks": len(docs),
                "chunk_id": doc_id,
            }
            await store_aput(
                store=self.store,
                namespace=namespace,
                key=doc_id,
                content=doc.page_content,
                metadata=chunk_metadata,
                index=["content"],
                retries=2,
                retry_backoff_s=0.5,
            )

        logger.info(f"章节已存储: {base_key}, 分块数: {len(docs)}")
        return doc_ids

    async def chapter_full_exists(self, chapter_number: int, chapter_title: str) -> bool:
        """检查章节全文键是否存在。"""
        try:
            namespace = self.get_chapters_namespace()
            key = self.get_chapter_full_key(chapter_title, chapter_number)
            res = await store_aget(store=self.store, namespace=namespace, key=key)
            return bool(res)
        except Exception:
            return False

    async def store_stream_segment(self, *, batch_id: str, chunk_index: int, content: str) -> str:
        """
        存储流式写作的分块内容，便于异步入库与向量检索。
        - 命名空间： ("writer_app_v2", book_id, "stream")
        - key: f"batch_{batch_id}_chunk_{chunk_index:03d}"
        - metadata: {content_type=stream_chunk, batch_id, chunk_index, schema_version=2}
        返回写入使用的 key。
        """
        namespace = self.get_stream_namespace()
        key = f"batch_{batch_id}_chunk_{chunk_index:03d}"
        base_metadata = {
            "book_id": self.book_id,
            "content_type": "stream_chunk",
            "batch_id": batch_id,
            "chunk_index": int(chunk_index),
            "created_at": get_db_timestamp(),
            "schema_version": 2,
        }
        await store_aput(
            store=self.store,
            namespace=namespace,
            key=key,
            content=content,
            metadata=base_metadata,
            index=["content"],
            retries=2,
            retry_backoff_s=0.5,
        )
        return key

    async def store_writer_plan(self, plan: WriterPlan):
        """
        存储策划案

        Args:
            store: 数据库存储实例
            plan: 策划案对象
        """
        namespace = self.get_plan_namespace()
        plan_key = self.get_plan_key()

        metadata = {
            "book_id": self.book_id,
            "book_name": plan.book_name,
            "book_description": plan.book_description,
            "created_at": get_db_timestamp(),
            "step_count": len(plan.plan_steps),
            "schema_version": 2,
        }

        # 存储完整的计划JSON
        plan_content = plan.model_dump_json()

        ret = await store_aput(
            store=self.store,
            namespace=namespace,
            key=plan_key,
            content=plan_content,
            metadata=metadata,
            index=[],
            retries=2,
            retry_backoff_s=0.5,
        )

        logger.info(f"策划案已存储: {metadata=}, key: {plan_key}, ret: {ret}")
        return ret

    async def store_refined_book_setting(self, refined_book_setting: str):
        """
        存储设定集

        Args:
            store: 数据库存储实例
            refined_book_setting: 设定集
        """
        namespace = self.get_settings_namespace()
        key = self.get_refined_book_setting_key()
        base_metadata = {
            "book_id": self.book_id,
            "created_at": get_db_timestamp(),
            "schema_version": 2,
        }

        ret = await store_aput(
            store=self.store,
            namespace=namespace,
            key=key,
            content=refined_book_setting,
            metadata=base_metadata,
            index=[],
            retries=2,
            retry_backoff_s=0.5,
        )

        # 分块存储以支持向量搜索
        docs = store_split_content_to_docs(long_content=refined_book_setting, metadata=base_metadata)
        doc_ids = []
        for i, doc in enumerate(docs):
            doc_id = f"{key}_chunk_{i}"
            doc_ids.append(doc_id)

            chunk_metadata = {
                **base_metadata,
                "chunk_type": "partial",
                "chunk_index": i,
                "total_chunks": len(docs),
                "chunk_id": doc_id,
            }

            await store_aput(
                store=self.store,
                namespace=namespace,
                key=doc_id,
                content=doc.page_content,
                metadata=chunk_metadata,
                index=["content"],
                retries=2,
                retry_backoff_s=0.5,
            )

        logger.info(f"向量已存储: {key}, 分块数: {len(docs)}")

        return ret

    async def get_writer_plan(self) -> Optional[WriterPlan]:
        """
        获取策划案

        Returns:
            策划案对象，如果不存在则返回None
        """
        namespace = self.get_plan_namespace()
        plan_key = self.get_plan_key()

        result = await store_aget(store=self.store, namespace=namespace, key=plan_key)

        if not result:
            return None

        try:
            plan_json = json.loads(result.value["content"])
            return WriterPlan.model_validate(plan_json)
        except Exception as e:
            logger.error(f"解析策划案失败: {e}")
            return None

    async def store_step_result(
        self,
        step: WriterPlanStep,
        result_content: str,
    ) -> List[str]:
        """
        存储步骤执行结果，并进行向量化

        Args:
            step: 步骤对象
            result_content: 执行结果内容

        Returns:
            存储的文档ID列表
        """
        namespace = self.get_settings_namespace()
        base_key = self.get_step_base_key(step.step_title)

        # 基础元数据
        base_metadata = {
            "book_id": self.book_id,
            "step_title": step.step_title,
            "step_description": step.step_description,
            "content_type": "step_result",
            "created_at": get_db_timestamp(),
            "schema_version": 2,
        }

        # 存储完整的原始结果
        await store_aput(
            store=self.store,
            namespace=namespace,
            key=self.get_step_full_key(step.step_title),
            content=result_content,
            metadata={**base_metadata, "chunk_type": "full"},
            index=[],
        )

        # 分块存储以支持向量搜索
        docs = store_split_content_to_docs(long_content=result_content, metadata=base_metadata)

        doc_ids = []
        for i, doc in enumerate(docs):
            doc_id = self.get_step_chunk_key(step.step_title, i)
            doc_ids.append(doc_id)

            chunk_metadata = {
                **base_metadata,
                "chunk_type": "partial",
                "chunk_index": i,
                "total_chunks": len(docs),
                "chunk_id": doc_id,
            }

            await store_aput(
                store=self.store,
                namespace=namespace,
                key=doc_id,
                content=doc.page_content,
                metadata=chunk_metadata,
                index=["content"],
                retries=2,
                retry_backoff_s=0.5,
            )

        logger.info(f"步骤结果已存储: {base_key}, 分块数: {len(docs)}")
        return doc_ids

    async def search_settings(self, query: str, limit: int = 5) -> List:
        """
        搜索相关设定内容

        Args:
            store: 数据库存储实例
            query: 搜索查询
            limit: 返回结果限制
            step_filter: 步骤过滤器，只搜索特定步骤的内容

        Returns:
            搜索结果列表
        """
        namespace = self.get_settings_namespace()

        # 执行语义搜索
        results = await store_asearch(self.store, namespace, query=query, limit=limit)

        return results

    async def get_step_result(self, step_title: str, full_content: bool = True) -> Optional[str]:
        """
        获取特定步骤的执行结果

        Args:
            step_title: 步骤标题
            full_content: 是否获取完整内容（否则返回分块内容）

        Returns:
            步骤执行结果内容
        """
        namespace = self.get_settings_namespace()
        base_key = self.get_step_base_key(step_title)

        if full_content:
            key = self.get_step_full_key(step_title)
            result = await store_aget(store=self.store, namespace=namespace, key=key)
            return result.value["content"] if result else None
        else:
            # 获取所有分块并组合
            # 这里可以实现分块内容的重新组合逻辑
            # 暂时返回完整内容
            return await self.get_step_result(step_title, True)

    async def store_volume_outline(self, book_detail: BookDetail):
        """
        存储书籍大纲

        Args:
            outline: 书籍大纲对象
        """
        namespace = self.get_outline_namespace()
        outline_key = self.get_volume_outline_key()

        metadata = {
            "book_id": self.book_id,
            "created_at": get_db_timestamp(),
            "schema_version": 2,
        }

        # 存储完整的大纲JSON
        outline_content = book_detail.model_dump_json()

        await store_aput(
            store=self.store,
            namespace=namespace,
            key=outline_key,
            content=outline_content,
            metadata=metadata,
            index=[],
            retries=2,
            retry_backoff_s=0.5,
        )

        logger.info(f"书籍大纲已存储: {metadata=}, key: {outline_key}")

    async def store_scene_design(self, scene_design):
        """
        存储场景设计

        Args:
            scene_design: 场景设计对象
        """
        namespace = self.get_scene_namespace()
        scene_key = self.get_scene_design_key()

        metadata = {
            "book_id": self.book_id,
            "volume_count": len(scene_design.volume_scenes),
            "completed_volumes": sum(1 for vs in scene_design.volume_scenes if vs.design_completed),
            "created_at": get_db_timestamp(),
            "schema_version": 2,
        }

        # 存储完整的场景设计JSON
        scene_content = scene_design.model_dump_json()

        await store_aput(
            store=self.store,
            namespace=namespace,
            key=scene_key,
            content=scene_content,
            metadata=metadata,
            index=[],
            retries=2,
            retry_backoff_s=0.5,
        )

        logger.info(f"场景设计已存储: {metadata=}, key: {scene_key}")

        # 分块存储场景内容以支持向量搜索
        scene_text = scene_design.get_all_scenes_content()
        base_metadata = {
            "book_id": self.book_id,
            "content_type": "scene_design",
            "created_at": get_db_timestamp(),
            "schema_version": 2,
        }

        docs = store_split_content_to_docs(long_content=scene_text, metadata=base_metadata)
        doc_ids = []
        for i, doc in enumerate(docs):
            doc_id = f"{scene_key}_chunk_{i}"
            doc_ids.append(doc_id)

            chunk_metadata = {
                **base_metadata,
                "chunk_type": "partial",
                "chunk_index": i,
                "total_chunks": len(docs),
                "chunk_id": doc_id,
            }

            await store_aput(
                store=self.store,
                namespace=namespace,
                key=doc_id,
                content=doc.page_content,
                metadata=chunk_metadata,
                index=["content"],
            )

        logger.info(f"场景设计向量已存储: {scene_key}, 分块数: {len(docs)}")

    async def get_scene_design(self):
        """
        获取场景设计

        Returns:
            场景设计对象，如果不存在则返回None
        """
        from src.nodes.common.book_types import BookSceneDesign

        namespace = self.get_scene_namespace()
        scene_key = self.get_scene_design_key()

        result = await store_aget(store=self.store, namespace=namespace, key=scene_key)

        if not result:
            return None

        try:
            scene_json = json.loads(result.value["content"])
            return BookSceneDesign.model_validate(scene_json)
        except Exception as e:
            logger.error(f"解析场景设计失败: {e}")
            return None


__all__ = ["WriterStoreManager", "StateHistory"]
