from typing import Literal, Optional

from langgraph.config import RunnableConfig
from langgraph.types import Command, Send

from src.context.context_builder import ContextBuilder
from src.nodes.chapter.stream_write_prompt import segment_prompt_template
from src.nodes.common.book_types import BookDetail, Chapter
from src.nodes.common.node_kit import build_resume_info, llm_json, rel_path_for_chapter, write_text_under_book
from src.state import State


def _build_segment_messages(state: State, config: RunnableConfig) -> list:
    book_detail: BookDetail = state.get("book_detail")
    min_len = config.get("configurable", {}).get("segment_min_len", 3000)
    max_len = config.get("configurable", {}).get("segment_max_len", 5000)

    cb = ContextBuilder()
    cb.header()
    cb.section(
        "智能分章任务",
        segment_prompt_template.format(
            book_name=(
                state.get("writer_current_plan").book_name
                if state.get("writer_current_plan")
                else state.get("book_name") or ""
            ),
            buffer_text=(book_detail.stream_buffer if book_detail and book_detail.stream_buffer else ""),
            min_len=min_len,
            max_len=max_len,
        ),
    )
    cb.json_schema(Chapter.model_json_schema())
    budget = config.get("configurable", {}).get("prompt_budget_tokens")
    return cb.build(max_tokens=budget, policy="chapter_segment")


async def chapter_segment_fn(
    state: State, config: RunnableConfig
) -> Command[Literal["stream_write", "chapter_segment", "__end__"]]:
    # 前置检查
    book_detail: BookDetail | None = state.get("book_detail")
    if not book_detail or not book_detail.stream_buffer:
        raise AssertionError("❌ 缓冲为空，无法分章")

    # 构建消息并请求 LLM（结构化）
    messages = _build_segment_messages(state, config)
    parsed: Chapter = await llm_json("chapter_segment", messages, Chapter, config=config)

    # 写入章节编号并追加
    next_index = (len(book_detail.chapters) + 1) if book_detail and book_detail.chapters else 1
    parsed.chapter_number = next_index
    book_detail.chapters.append(parsed)

    # 从缓冲扣除已消费字数
    consumed = len(parsed.chapter_content or "")
    remaining = (book_detail.stream_buffer or "")[consumed:]
    if remaining.startswith("\n"):
        remaining = remaining.lstrip()
    book_detail.stream_buffer = remaining

    # 写入章节文件
    file_rel = rel_path_for_chapter(next_index, parsed.chapter_title)
    write_text_under_book(state.get("book_name"), state.get("book_id"), file_rel, parsed.get_completed_content())
    # 入库：改为异步单元任务（不阻塞主路径）。实际派发由 join 节点根据并发上限调度
    add_tasks: list[Send] = []
    expected_numbers = set()
    try:
        expected_numbers = set(int(x) for x in (state.get("chapter_store_expected_numbers") or []))
    except Exception:
        expected_numbers = set()
    expected_numbers.add(int(next_index))
    expected_list = sorted(list(expected_numbers))
    # 准备待派发队列与已派发集合
    dispatched = set()
    try:
        for n in state.get("chapter_store_dispatched_numbers") or []:
            dispatched.add(int(n))
    except Exception:
        dispatched = set()
    pending_numbers = [n for n in expected_list if n not in dispatched]

    # 终止条件：达到目标章数
    try:
        target_cnt = config.get("configurable", {}).get("write_chapters_cnt")
        if target_cnt is not None and len(book_detail.chapters) >= int(target_cnt):
            update = {
                "book_detail": book_detail,
                "chapter_store_expected_numbers": expected_list,
                "chapter_store_pending_numbers": pending_numbers,
                "chapter_store_dispatched_numbers": sorted(list(dispatched)),
                "resume_info": build_resume_info(
                    state,
                    node="chapter_segment",
                    next_node="writer_chapter_store_join",
                    summary=f"智能分章：生成第{next_index}章，缓冲{len(book_detail.stream_buffer)}字 -> 章节入库汇总",
                ),
            }
            return Command(update=update, goto=[Send("writer_chapter_store_join", {}), "writer_chapter_store_join"])
    except Exception:
        pass

    # 决定下一步
    min_len = config.get("configurable", {}).get("segment_min_len", 3000)
    next_node = "chapter_segment" if len(book_detail.stream_buffer) >= min_len else "stream_write"

    update = {
        "book_detail": book_detail,
        "chapter_store_expected_numbers": expected_list,
        "chapter_store_pending_numbers": pending_numbers,
        "chapter_store_dispatched_numbers": sorted(list(dispatched)),
        "resume_info": build_resume_info(
            state,
            node="chapter_segment",
            next_node=next_node,
            summary=f"智能分章：生成第{next_index}章，缓冲{len(book_detail.stream_buffer)}字 -> {next_node}",
            chapter_number=next_index,
            buffer_len=len(book_detail.stream_buffer),
        ),
    }
    return Command(update=update, goto=add_tasks + [next_node])


__all__ = ["chapter_segment_fn"]
