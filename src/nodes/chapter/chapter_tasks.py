from __future__ import annotations

import json
import time

from typing import Literal, Optional

from langgraph.config import RunnableConfig
from langgraph.types import Command, Send
from src.common.utils.filesystem_utils import FileSystemUtils

from src.nodes.common.book_types import BookDetail, Chapter
from src.nodes.common.node_kit import build_resume_info, save_chapter_to_db
from src.state import State


async def writer_chapter_store_one_task(state: State, config: RunnableConfig) -> Command[Literal["__end__"]]:
    """
    单元任务：将指定章节入库（全文 + 分块向量化）。
    读取：state.task_chapter_number、state.book_id、state.book_detail
    返回：将结果写入 state.chapter_store_result_<n>，并结束该支路。
    """
    chapter_number = int(state.get("task_chapter_number") or 0)
    book_id: Optional[str] = state.get("book_id")
    book_detail: Optional[BookDetail] = state.get("book_detail")

    result_key = f"chapter_store_result_{chapter_number}"
    if not book_id or not book_detail or chapter_number <= 0:
        return Command(update={result_key: {"ok": False, "reason": "invalid_state"}}, goto="__end__")

    target: Optional[Chapter] = None
    try:
        for ch in book_detail.chapters or []:
            if getattr(ch, "chapter_number", None) == chapter_number:
                target = ch
                break
    except Exception:
        target = None

    if target is None:
        return Command(update={result_key: {"ok": False, "reason": "chapter_not_found"}}, goto="__end__")

    ok = True
    try:
        await save_chapter_to_db(book_id, target)
    except Exception as e:
        ok = False
        # 非致命，记录失败原因
        return Command(update={result_key: {"ok": ok, "error": str(e)}}, goto="__end__")

    return Command(update={result_key: {"ok": ok}}, goto="__end__")


async def writer_chapter_store_join_node(
    state: State, config: RunnableConfig
) -> Command[Literal["__end__", "writer_chapter_store_join"]]:
    """
    章节入库并发 Join：
    - 等待 state.chapter_store_expected_numbers 中的所有 `chapter_store_result_<n>` 出现
    - 汇总并在 resume_info 中记录 [completed/total]
    - 结束于 __end__（不改变主流程写作顺序）
    """
    # 期望集合
    expected_numbers = set()
    try:
        for n in state.get("chapter_store_expected_numbers") or []:
            expected_numbers.add(int(n))
    except Exception:
        expected_numbers = set()

    # 已有结果
    have_numbers = set()
    results = {}
    try:
        for k, v in state.items():
            if isinstance(k, str) and k.startswith("chapter_store_result_"):
                try:
                    n = int(k.split("_")[-1])
                except Exception:
                    continue
                have_numbers.add(n)
                results[n] = v
    except Exception:
        pass

    # 并发限流：根据配置逐批派发剩余任务（安全转换，过滤无效值）
    def safe_int_list(values) -> list[int]:
        result = []
        for x in values or []:
            try:
                result.append(int(x))
            except (ValueError, TypeError):
                continue  # 跳过无效值
        return result

    pending_numbers: list[int] = safe_int_list(state.get("chapter_store_pending_numbers"))
    dispatched_numbers: list[int] = safe_int_list(state.get("chapter_store_dispatched_numbers"))
    max_concurrency = int((config.get("configurable") or {}).get("chapter_store_max_concurrency", 4))
    requeue_ttl_ms = int((config.get("configurable") or {}).get("chapter_store_requeue_ttl_ms", 30000))
    requeue_max = int((config.get("configurable") or {}).get("chapter_store_requeue_max", 2))
    dispatch_times: dict = dict(state.get("chapter_store_dispatch_times") or {})
    requeue_counts: dict = dict(state.get("chapter_store_requeue_counts") or {})

    in_flight_indices = [i for i in dispatched_numbers if i not in have_numbers]
    in_flight = len(in_flight_indices)
    capacity = max(0, max_concurrency - in_flight)
    add_tasks: list[Send] = []

    # 先尝试对超时在飞任务进行重派发（requeue 优先），超阈值则降级为失败
    now_ms = int(time.time() * 1000)
    to_requeue: list[int] = []
    to_mark_fail: list[int] = []
    if capacity > 0 and in_flight_indices:
        for i in in_flight_indices:
            last_ms = int(dispatch_times.get(str(i)) or dispatch_times.get(i) or 0)
            cnt = int(requeue_counts.get(str(i)) or requeue_counts.get(i) or 0)
            if last_ms <= 0:
                last_ms = 0
            elapsed_ok = (now_ms - last_ms) >= max(0, requeue_ttl_ms)
            if elapsed_ok and cnt < max(0, requeue_max):
                to_requeue.append(i)
                if len(to_requeue) >= capacity:
                    break
            elif elapsed_ok and cnt >= max(0, requeue_max):
                to_mark_fail.append(i)
    if to_requeue:
        for i in to_requeue:
            add_tasks.append(Send("writer_chapter_store_one", {"task_chapter_number": i}))
            dispatched_numbers = sorted(list(set(dispatched_numbers + [i])))
            dispatch_times[str(i)] = now_ms
            requeue_counts[str(i)] = int(requeue_counts.get(str(i), 0)) + 1
        capacity -= len(to_requeue)

    if capacity > 0 and pending_numbers:
        to_dispatch = pending_numbers[:capacity]
        add_tasks.extend([Send("writer_chapter_store_one", {"task_chapter_number": i}) for i in to_dispatch])
        dispatched_numbers = sorted(list(set(dispatched_numbers + to_dispatch)))
        for i in to_dispatch:
            dispatch_times[str(i)] = now_ms
        pending_numbers = pending_numbers[capacity:]

    if expected_numbers and not expected_numbers.issubset(have_numbers):
        # 降级为失败：生成占位结果，避免无限等待
        update_fail_results: dict = {}
        if to_mark_fail:
            for i in to_mark_fail:
                result_key = f"chapter_store_result_{i}"
                update_fail_results[result_key] = {"ok": False, "error": "requeue_exceeded"}
        # 从配置读取预览长度
        preview_len = int(config.get("configurable", {}).get("queue_preview_len", 5))
        update_partial = {
            "chapter_store_pending_numbers": pending_numbers,
            "chapter_store_dispatched_numbers": dispatched_numbers,
            "chapter_store_dispatch_times": dispatch_times,
            "chapter_store_requeue_counts": requeue_counts,
            # 观测：在飞明细（预览）
            "chapter_store_inflight_count": len([i for i in dispatched_numbers if i not in have_numbers]),
            "chapter_store_inflight_preview": [int(i) for i in in_flight_indices[:preview_len]],
        }
        if update_fail_results:
            update_partial.update(update_fail_results)
        return Command(update=update_partial, goto=add_tasks + ["writer_chapter_store_join"])

    total = len(expected_numbers)
    completed = 0
    for n in expected_numbers:
        try:
            if bool((results.get(n) or {}).get("ok", False)):
                completed += 1
        except Exception:
            pass

    # 已移除：失败清单与摘要快照落盘（废弃 snapshot 功能）

    update = {
        "resume_info": build_resume_info(
            state,
            node="writer_chapter_store_join",
            next_node="__end__",
            summary=f"章节入库完成 [{completed}/{total}] -> 结束",
            total=total,
            completed=completed,
        ),
        # 清理派发队列状态
        "chapter_store_pending_numbers": [],
        "chapter_store_dispatched_numbers": [],
        "chapter_store_dispatch_times": {},
        "chapter_store_requeue_counts": {},
    }
    return Command(update=update, goto="__end__")


__all__ = [
    "writer_chapter_store_one_task",
    "writer_chapter_store_join_node",
]
