from __future__ import annotations

import time

from typing import Literal, Optional

from langgraph.config import RunnableConfig
from langgraph.types import Command, Send

from src.nodes.common.node_kit import build_resume_info, write_text_under_book
from src.nodes.common.store_manager import Writer<PERSON><PERSON>Manager
from src.state import State


def _safe_get_chunk(state: State, index: int) -> str:
    try:
        chunks = state.get("stream_post_chunks") or []
        if 0 <= index < len(chunks):
            return str(chunks[index])
    except Exception:
        pass
    # 兜底：使用当前缓冲（可能很长，不建议常用）
    book_detail = state.get("book_detail")
    return book_detail.stream_buffer if book_detail and book_detail.stream_buffer else ""


async def writer_stream_post_one_task(
    state: State, config: RunnableConfig
) -> Command[Literal["writer_stream_post_join"]]:
    """
    单元后处理任务：处理一个流式分块。
    读取：state.stream_post_batch_id, state.task_chunk_index, state.stream_post_chunks
    动作：
      - 将分块写入 workspace: chapters_stream/batch-<batch_id>/chunk-<idx+1>.md
      - 入库：("writer_app_v2", book_id, "stream") 命名空间，content_type=stream_chunk
      - 写入 state 键：stream_post_result_<idx> = {"chunk_index": idx, "doc_key": key}
    返回：转到 join 节点等待聚合
    """
    batch_id = str(state.get("stream_post_batch_id") or "unknown")
    idx = int(state.get("task_chunk_index") or 0)

    book_name = state.get("book_name") or (
        state.get("writer_current_plan").book_name if state.get("writer_current_plan") else ""
    )
    book_id: Optional[str] = state.get("book_id")

    chunk_text = _safe_get_chunk(state, idx)

    # 写入文件（幂等覆盖）
    rel_path = f"chapters_stream/batch-{batch_id}/chunk-{idx + 1:03d}.md"
    _ = write_text_under_book(book_name, book_id, rel_path, chunk_text)

    # 入库（流分块）
    doc_key: Optional[str] = None
    try:
        if book_id:
            sm = WriterStoreManager(book_id=book_id)
            doc_key = await sm.store_stream_segment(batch_id=batch_id, chunk_index=idx, content=chunk_text)
    except Exception:
        # 入库失败不影响主流程
        pass

    result_key = f"stream_post_result_{idx}"
    update = {result_key: {"chunk_index": idx, "file": rel_path, "doc_key": doc_key}}
    return Command(update=update, goto="writer_stream_post_join")


async def writer_stream_post_spawn_tasks(
    state: State, indices: list[int]
) -> Command[Literal["writer_stream_post_join"]]:
    """
    根据给定的分块索引派发任务。要求调用方已在 state 中写入：
      - stream_post_batch_id
      - stream_post_chunks
    这里仅负责派发 Send 任务并记录期望集合。
    """
    tasks = [Send("writer_stream_post_one", {"task_chunk_index": i}) for i in indices]
    update = {"stream_post_expected_indices": indices}
    return Command(update=update, goto=tasks + ["writer_stream_post_join"])


async def writer_stream_post_join_node(
    state: State, config: RunnableConfig
) -> Command[Literal["chapter_segment", "writer_stream_post_join"]]:
    """
    汇总后处理任务结果：
      - 等待所有 stream_post_result_<i>
      - 生成批量汇总文件（可用于审阅索引）：chapters_stream/batch-<batch_id>/批量-流式后处理.md
      - 记录 resume_info
      - 跳转至 chapter_segment（不改变原有顺序逻辑）
    """
    expected = set(int(x) for x in (state.get("stream_post_expected_indices") or []))
    have: set[int] = set()
    result_items: list[dict] = []
    for k, v in state.items():
        if isinstance(k, str) and k.startswith("stream_post_result_") and isinstance(v, dict):
            try:
                i = int(k.split("_")[-1])
            except Exception:
                continue
            have.add(i)
            result_items.append({"index": i, **v})

    # 并发限流：根据配置逐批派发剩余任务（安全转换，过滤无效值）
    def safe_int_list(values) -> list[int]:
        result = []
        for x in values or []:
            try:
                result.append(int(x))
            except (ValueError, TypeError):
                continue  # 跳过无效值
        return result

    pending: list[int] = safe_int_list(state.get("stream_post_pending_indices"))
    dispatched: list[int] = safe_int_list(state.get("stream_post_dispatched_indices"))
    max_concurrency = int((config.get("configurable") or {}).get("stream_post_max_concurrency", 4))
    requeue_ttl_ms = int((config.get("configurable") or {}).get("stream_post_requeue_ttl_ms", 30000))
    requeue_max = int((config.get("configurable") or {}).get("stream_post_requeue_max", 2))
    dispatch_times: dict = dict(state.get("stream_post_dispatch_times") or {})
    requeue_counts: dict = dict(state.get("stream_post_requeue_counts") or {})

    # 计算当前在飞任务数
    in_flight_indices = [i for i in dispatched if i not in have]
    in_flight = len(in_flight_indices)
    capacity = max(0, max_concurrency - in_flight)
    add_tasks: list[Send] = []

    # 先尝试对超时在飞任务进行重派发（requeue 优先），超阈值则降级为失败
    now_ms = int(time.time() * 1000)
    to_requeue: list[int] = []
    to_mark_fail: list[int] = []
    if capacity > 0 and in_flight_indices:
        for i in in_flight_indices:
            last_ms = int(dispatch_times.get(str(i)) or dispatch_times.get(i) or 0)
            cnt = int(requeue_counts.get(str(i)) or requeue_counts.get(i) or 0)
            if last_ms <= 0:
                # 未记录时间视为超时，确保进入正常派发路径
                last_ms = 0
            elapsed_ok = (now_ms - last_ms) >= max(0, requeue_ttl_ms)
            if elapsed_ok and cnt < max(0, requeue_max):
                to_requeue.append(i)
                if len(to_requeue) >= capacity:
                    break
            elif elapsed_ok and cnt >= max(0, requeue_max):
                to_mark_fail.append(i)
    if to_requeue:
        for i in to_requeue:
            add_tasks.append(Send("writer_stream_post_one", {"task_chunk_index": i}))
            dispatched = sorted(list(set(dispatched + [i])))
            dispatch_times[str(i)] = now_ms
            requeue_counts[str(i)] = int(requeue_counts.get(str(i), 0)) + 1
        capacity -= len(to_requeue)

    # 降级为失败：生成占位结果，避免无限等待
    update_fail_results: dict = {}
    if to_mark_fail:
        batch_id = str(state.get("stream_post_batch_id") or "unknown")
        for i in to_mark_fail:
            result_key = f"stream_post_result_{i}"
            rel_file = f"chapters_stream/batch-{batch_id}/chunk-{i + 1:03d}.md"
            update_fail_results[result_key] = {
                "chunk_index": i,
                "file": rel_file,
                "doc_key": None,
                "error": "requeue_exceeded",
            }
    if capacity > 0 and pending:
        # 取前 capacity 个未派发索引进行派发
        to_dispatch = pending[:capacity]
        add_tasks.extend([Send("writer_stream_post_one", {"task_chunk_index": i}) for i in to_dispatch])
        dispatched = sorted(list(set(dispatched + to_dispatch)))
        for i in to_dispatch:
            dispatch_times[str(i)] = now_ms
        pending = pending[capacity:]

    # 若仍未全部完成，继续等待
    if expected and not expected.issubset(have):
        in_flight_now = [i for i in dispatched if i not in have]
        # 从配置读取预览长度
        preview_len = int(config.get("configurable", {}).get("queue_preview_len", 5))
        update_partial = {
            "stream_post_pending_indices": pending,
            "stream_post_dispatched_indices": dispatched,
            "stream_post_dispatch_times": dispatch_times,
            "stream_post_requeue_counts": requeue_counts,
            # 观测：在飞明细（预览）
            "stream_post_inflight_count": len(in_flight_now),
            "stream_post_inflight_preview": [int(i) for i in in_flight_now[:preview_len]],
        }
        if update_fail_results:
            update_partial.update(update_fail_results)
        return Command(update=update_partial, goto=add_tasks + ["writer_stream_post_join"])

    # 批量文件
    batch_id = str(state.get("stream_post_batch_id") or "unknown")
    book_name = state.get("book_name") or (
        state.get("writer_current_plan").book_name if state.get("writer_current_plan") else ""
    )
    book_id = state.get("book_id")

    # 排序输出
    result_items.sort(key=lambda x: int(x.get("index", 0)))
    lines: list[str] = ["# 流式后处理批量结果", "", f"批次: {batch_id}", "", "---", ""]
    for item in result_items:
        idx = int(item.get("index", 0))
        rel_file = item.get("file") or f"chapters_stream/batch-{batch_id}/chunk-{idx + 1:03d}.md"
        doc_key = item.get("doc_key") or "-"
        lines.append(f"- 分块 {idx + 1:03d}: 文件 {rel_file} | 文档键 {doc_key}")
    summary_rel = f"chapters_stream/batch-{batch_id}/批量-流式后处理.md"
    _ = write_text_under_book(book_name, book_id, summary_rel, "\n".join(lines))

    # 已移除：快照级汇总（废弃 snapshot 功能）

    # resume_info 记录
    buffer_len = 0
    try:
        bd = state.get("book_detail")
        buffer_len = len(bd.stream_buffer or "") if bd else 0
    except Exception:
        buffer_len = 0

    update = {
        "resume_info": build_resume_info(
            state,
            node="writer_stream_post_join",
            next_node="chapter_segment",
            summary=f"流式后处理完成（{len(result_items)} 个分块） -> chapter_segment",
            buffer_len=buffer_len,
            chunks_count=len(result_items),
            post_batch_id=batch_id,
        ),
        # 清理派发队列状态
        "stream_post_pending_indices": [],
        "stream_post_dispatched_indices": [],
        "stream_post_dispatch_times": {},
        "stream_post_requeue_counts": {},
    }
    return Command(update=update, goto="chapter_segment")


__all__ = [
    "writer_stream_post_one_task",
    "writer_stream_post_join_node",
]
