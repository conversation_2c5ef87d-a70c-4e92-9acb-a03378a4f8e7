from typing import Literal

from langgraph.config import RunnableConfig
from langgraph.types import Command, Send

from src.context.context_builder import Con<PERSON><PERSON><PERSON><PERSON>
from src.context.policies import STREAM_WRITING_GUIDANCE
from src.nodes.common.book_types import BookDetail, Chapter
from src.nodes.common.node_kit import build_stream_resume_info, llm_text, maybe_interrupt_review, write_text_under_book
from src.state import State


def _build_stream_messages(state: State, config: RunnableConfig):
    book_detail: BookDetail = state.get("book_detail")
    refined_book_setting = state.get("refined_book_setting")
    book_name = state.get("book_name") or state.get("writer_current_plan").book_name

    recent_buffer = book_detail.stream_buffer[-1000:] if book_detail and book_detail.stream_buffer else ""

    current_idx = (len(book_detail.chapters) + 1) if book_detail else 1
    vo = book_detail.get_volume_outline_for_chapter(current_idx) if book_detail else None
    volume_outline = vo.get_completed_contents() if vo else "（当前卷大纲未就绪）"

    volume_core_summary = ""
    if vo:
        theme_text = vo.volume_title or f"第{vo.volume_number}卷"
        conflict_summary = "主角与强敌/环境/规则的对抗"
        scene_design = state.get("scene_design", None)
        if scene_design and hasattr(scene_design, "get_volume_scenes"):
            try:
                volume_scenes = scene_design.get_volume_scenes(vo.volume_number)
                if volume_scenes and volume_scenes.scenes:
                    all_conflicts = []
                    for sc in volume_scenes.scenes:
                        if getattr(sc, "conflicts", None):
                            all_conflicts.extend([c for c in sc.conflicts if c and c.strip()])
                    seen = set()
                    dedup = []
                    for c in all_conflicts:
                        if c not in seen:
                            seen.add(c)
                            dedup.append(c)
                    if dedup:
                        conflict_summary = ", ".join(dedup[:2])
            except Exception:
                pass
        goal_text = f"完成第{vo.volume_number}卷阶段目标，推进主线与角色成长"
        volume_core_summary = "\n".join(
            [f"- 主题：{theme_text}", f"- 核心冲突：{conflict_summary}", f"- 目标：{goal_text}"]
        )

    scene_info = ""
    related_characters = ""
    recent_summaries = []
    scene_design = state.get("scene_design", None)
    character_details = state.get("character_details", None)
    if scene_design and book_detail:
        scene = scene_design.get_scene_for_chapter(current_idx)
        if scene:
            scene_info = scene.get_scene_content()
            if character_details and scene.featured_characters:
                infos = []
                for name in scene.featured_characters:
                    char = character_details.get_character_by_name(name)
                    if char:
                        infos.append(char.get_full_content())
                related_characters = "\n\n".join(infos)
        for back in range(1, 4):
            prev_scene = scene_design.get_scene_for_chapter(current_idx - back)
            if prev_scene:
                recent_summaries.append(prev_scene.get_scene_content())
    if recent_summaries:
        scene_info = ("\n\n".join(recent_summaries) + "\n\n" + scene_info).strip()

    unresolved_short = ""
    if book_detail and book_detail.chapters:
        try:
            last_chapter = book_detail.chapters[-1]
            if getattr(last_chapter, "think_todo_outline", None):
                lines = [ln.strip() for ln in str(last_chapter.think_todo_outline).splitlines() if ln.strip()]
                if lines:
                    unresolved_short = "\n".join([f"- {ln}" for ln in lines[:5]])
        except Exception:
            unresolved_short = ""

    target_append_words = 1200

    cb = ContextBuilder()
    cb.header()
    cb.section(
        "写作模式", f"你是一位专业的玄幻爽文网络小说作家。我们采用“流式写作+智能分章”模式：\n{STREAM_WRITING_GUIDANCE}"
    )
    cb.section(
        "写作要求",
        f"- 续写不低于 {target_append_words} 字，保持内容连贯、信息密度高、节奏鲜明\n- 只生成正文，不要标题、不要分章、不要总结\n- 不要凭空引入未设定的角色（仅使用角色设定中已有角色）\n- 保持爽点密度、推进矛盾冲突、适时补充世界观信息",
    )
    cb.section("书籍信息", book_name)
    cb.section("设定集", refined_book_setting)
    if volume_core_summary:
        cb.section("卷级核心摘要（短）", volume_core_summary)
    cb.section("当前卷大纲（摘要）", volume_outline)
    cb.section("当前场景信息", scene_info or "本次续写无需限定场景，可自然推进")
    if unresolved_short:
        cb.section("未解悬念清单（短）", unresolved_short)
    cb.section("相关角色设定", related_characters or "本续写无需特定出场角色")
    cb.section("最近缓冲区内容（避免重复衔接，仅参考，不要复述）", recent_buffer)
    cb.json_schema(Chapter.model_json_schema())

    budget = config.get("configurable", {}).get("prompt_budget_tokens")
    return cb.build(max_tokens=budget, policy="stream")


async def stream_write_fn(
    state: State, config: RunnableConfig
) -> Command[Literal["stream_write", "chapter_segment", "writer_stream_post_join", "__end__"]]:
    plan = state.get("writer_current_plan")
    if not plan or not plan.is_all_step_completed():
        raise AssertionError("❌ 策划案未完成，无法进行流式写作")
    if state.get("book_id") is None:
        raise AssertionError("book_id 为空")
    if state.get("book_detail") is None:
        raise AssertionError("❌ 大纲未生成，无法进行流式写作")

    messages = _build_stream_messages(state, config)

    content, _ = await llm_text("stream_write", messages, config=config)

    book_detail: BookDetail = state.get("book_detail")
    if not book_detail.stream_buffer:
        book_detail.stream_buffer = content
    else:
        book_detail.stream_buffer += "\n\n" + content

    review_file = write_text_under_book(
        state.get("book_name"), state.get("book_id"), "chapters_stream/stream_buffer.md", book_detail.stream_buffer
    )

    # 达到阈值 → 注入人审
    max_buffer = 8000
    try:
        target_cnt = config.get("configurable", {}).get("write_chapters_cnt")
        if target_cnt is not None and len(book_detail.chapters) >= int(target_cnt):
            return Command(update={"book_detail": book_detail}, goto="__end__")
    except Exception:
        pass

    if len(book_detail.stream_buffer) >= max_buffer:
        hr_update, next_node = await maybe_interrupt_review(
            state=state,
            config=config,
            node_name="stream_write",
            input_messages=messages,
            review_file_name=review_file,
            review_content=book_detail.stream_buffer,
            next_node_if_pass="chapter_segment",
        )
        if hr_update is None:
            # 未启用 interrupt 审核：直接跳到分章
            hr_update, next_node = {}, "chapter_segment"
        # 当进入分章前，派发流式后处理任务（按固定分块大小切分当前缓冲快照）
        # 仅当下一步将进入分章时才派发，避免在继续 stream_write 的路径上产生副作用
        target_node_after_join = "chapter_segment"
        chunk_size = int((config.get("configurable", {}) or {}).get("stream_post_chunk_size", 2000))
        buffer_snapshot = str(book_detail.stream_buffer or "")
        chunks: list[str] = []
        if chunk_size > 0 and len(buffer_snapshot) > 0:
            for i in range(0, len(buffer_snapshot), chunk_size):
                chunks.append(buffer_snapshot[i : i + chunk_size])
        indices = list(range(len(chunks)))
        # 将本批次信息写入 state，并派发任务
        update = {"book_detail": book_detail}
        update.update(hr_update)
        batch_id = str(len(book_detail.chapters) + 1)
        if indices:
            update.update(
                {
                    "stream_post_batch_id": batch_id,
                    "stream_post_chunks": chunks,
                    "stream_post_expected_indices": indices,
                    # 采用“队列 + 限流”模式：首批派发由 join 节点根据并发上限发起
                    "stream_post_pending_indices": indices,
                    "stream_post_dispatched_indices": [],
                    "resume_info": build_stream_resume_info(
                        state,
                        node="stream_write",
                        next_node="writer_stream_post_join",
                        buffer_len=len(book_detail.stream_buffer),
                        chunks_count=len(indices),
                    ),
                }
            )
            # 不在此处派发任务，由 writer_stream_post_join 节点根据并发上限调度
            return Command(update=update, goto="writer_stream_post_join")
        else:
            # 无需后处理，直接进入下一节点
            update["resume_info"] = build_stream_resume_info(
                state,
                node="stream_write",
                next_node=next_node,
                buffer_len=len(book_detail.stream_buffer),
            )
            return Command(update=update, goto=next_node)

    # 继续流式写作
    update = {
        "book_detail": book_detail,
        "resume_info": build_stream_resume_info(
            state,
            node="stream_write",
            next_node="stream_write",
            buffer_len=len(book_detail.stream_buffer),
        ),
    }
    return Command(update=update, goto="stream_write")
