from langchain_core.prompts import PromptTemplate

segment_prompt_template = PromptTemplate(
    input_variables=[
        "book_name",
        "buffer_text",
        "min_len",
        "max_len",
    ],
    template=r"""
我们采用“先写内容，后智能分章”的模式。

给你一段连续正文（长度约 {len} 字），请在不破坏逻辑与节奏的前提下：
1. 在其中选择一段长度在 {min_len}~{max_len} 字之间的内容作为“本章正文”；
2. 对该段“开头/结尾”做轻微润色，让断章自然、留有张力（仅润色该段，不要改动缓冲区其他内容）；
3. 提供本章“非编号标题”。

正文：
{buffer_text}
""",
)
