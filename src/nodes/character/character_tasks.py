from __future__ import annotations

from typing import Literal, Optional

from langgraph.config import RunnableConfig
from langgraph.types import Command, Send

from src.context.context_builder import ContextBuilder
from src.nodes.character.character_prompt import character_detailed_design_template
from src.nodes.common.book_types import CharacterDetail, CharacterDetailCollection, CharacterSummaryCollection
from src.nodes.common.node_kit import build_resume_info, llm_json, maybe_interrupt_review, write_text_under_book
from src.state import State


def _build_detail_messages(
    state: State, config: RunnableConfig, target_summary: str, other_characters: str, character_name: str
) -> list:
    plan = state.get("writer_current_plan")
    text = character_detailed_design_template.format(
        refined_book_setting=state.get("refined_book_setting", "暂无"),
        book_name=plan.book_name if plan else (state.get("book_name") or ""),
        book_description=(plan.book_description if plan else "") or "",
        character_name=character_name,
        target_summary=target_summary,
        other_characters=other_characters,
    )
    cb = ContextBuilder()
    cb.header()
    cb.section("角色细化任务", text)
    cb.json_schema(CharacterDetail.model_json_schema())
    budget = config.get("configurable", {}).get("prompt_budget_tokens")
    return cb.build(max_tokens=budget, policy="character")


async def writer_character_detail_one_task(
    state: State, config: RunnableConfig
) -> Command[Literal["writer_character_join"]]:
    """
    子任务：生成单个角色详情，输入为 state.task_target_name。
    输出写入 `character_task_result_<name>` 键。
    """
    summaries: CharacterSummaryCollection = state.get("character_summaries")
    name: str = state.get("task_target_name")
    if summaries is None or not getattr(summaries, "characters", None):
        # 回退
        return Command(goto="writer_character_join")

    # 目标与其他角色文本
    target = ""
    others: list[str] = []
    for s in summaries.characters:
        if s.name == name:
            target = s.get_full_content()
        else:
            others.append(s.get_full_content())

    messages = _build_detail_messages(state, config, target, "\n\n".join(others), name)
    detail = await llm_json("writer_character:detail", messages, CharacterDetail, config=config)
    try:
        if not getattr(detail, "tier", None):
            setattr(detail, "tier", "core")
    except Exception:
        pass

    book_name = state.get("book_name") or (
        state.get("writer_current_plan").book_name if state.get("writer_current_plan") else ""
    )
    book_id = state.get("book_id")
    # 单角色文件（以 summaries 中顺序命名，join 中也会再次覆盖确保一致）
    order_map = {s.name: i for i, s in enumerate(summaries.characters)}
    idx = order_map.get(name, 0) + 1
    pad = "0" if idx < 10 else ""
    rel = f"character/角色详情_{pad}{idx}_{name}.md"
    write_text_under_book(book_name, book_id, rel, f"# {detail.get_full_content()}\n")

    key = f"character_task_result_{name}"
    return Command(update={key: detail.model_dump()}, goto="writer_character_join")


async def writer_character_spawn_tasks(state: State, targets: list[str]) -> Command[Literal["writer_character_join"]]:
    tasks = [Send("writer_character_detail_one", {"task_target_name": name}) for name in targets]
    return Command(update={"character_task_expected_names": targets}, goto=tasks + ["writer_character_join"])


async def writer_character_join_node(
    state: State, config: RunnableConfig
) -> Command[Literal["writer_outline", "writer_character"]]:
    """
    聚合详情结果，写入 `character_details` 与汇总文件，并触发 interrupt 审核。
    """
    summaries: CharacterSummaryCollection = state.get("character_summaries")
    details: Optional[CharacterDetailCollection] = state.get("character_details")
    book_name = state.get("book_name") or (
        state.get("writer_current_plan").book_name if state.get("writer_current_plan") else ""
    )
    book_id = state.get("book_id")

    if summaries is None or not getattr(summaries, "characters", None):
        return Command(goto="writer_character")

    all_list = list(details.characters) if details and details.characters else []
    exist_map = {ch.name: idx for idx, ch in enumerate(all_list)}

    # 收集任务结果
    for k, v in list(state.items()):
        if isinstance(k, str) and k.startswith("character_task_result_") and isinstance(v, dict):
            try:
                detail = CharacterDetail.model_validate(v)
            except Exception:
                continue
            if detail.name in exist_map:
                all_list[exist_map[detail.name]] = detail
            else:
                all_list.append(detail)

    # 若任务未全部完成，则继续等待
    expected = set(state.get("character_task_expected_names") or [])
    have = set([ch.name for ch in all_list])
    if expected and not expected.issubset(have):
        return Command(goto="writer_character_join")

    new_details = CharacterDetailCollection(characters=all_list)

    # 单角色文件（确保按 summaries 顺序覆盖一遍）
    order_map = {s.name: i for i, s in enumerate(summaries.characters)}
    for ch in new_details.characters:
        idx = order_map.get(ch.name, 0) + 1
        pad = "0" if idx < 10 else ""
        rel = f"character/角色详情_{pad}{idx}_{ch.name}.md"
        write_text_under_book(book_name, book_id, rel, f"# {ch.get_full_content()}\n")

    # 汇总
    summary_rel = "character/角色详情.md"
    summary_file = write_text_under_book(book_name, book_id, summary_rel, new_details.get_characters_content())

    update = {"character_details": new_details}

    hr_update, next_node = await maybe_interrupt_review(
        state=state,
        config=config,
        node_name="writer_character",
        input_messages=[],
        review_file_name=summary_file,
        review_content=new_details.get_characters_content(),
        next_node_if_pass="writer_outline",
    )
    next_node = next_node or "writer_outline"
    if hr_update:
        update.update(hr_update)

    # 进度统计（用于 CLI 完成行显示 [completed/total]）
    try:
        total = len(summaries.characters) if summaries and getattr(summaries, "characters", None) else 0
    except Exception:
        total = 0
    try:
        completed = len(new_details.characters) if new_details and getattr(new_details, "characters", None) else 0
    except Exception:
        completed = 0

    update["resume_info"] = build_resume_info(
        state,
        node="writer_character",
        next_node=next_node,
        summary=f"角色并发生成（tasks） -> {next_node}",
        completed=completed,
        total=total,
    )
    return Command(update=update, goto=next_node)


__all__ = [
    "writer_character_detail_one_task",
    "writer_character_join_node",
    "writer_character_spawn_tasks",
]
