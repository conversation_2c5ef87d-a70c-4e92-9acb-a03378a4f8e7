from __future__ import annotations

from typing import Literal, Optional

from langgraph.config import RunnableConfig
from langgraph.types import Command

from src.context.context_builder import ContextBuilder
from src.nodes.character.character_prompt import core_character_design_template
from src.nodes.character.character_tasks import (
    writer_character_detail_one_task,
    writer_character_join_node,
    writer_character_spawn_tasks,
)
from src.nodes.common.book_types import (
    CharacterDetail,
    CharacterDetailCollection,
    CharacterSummaryCollection,
    WriterPlan,
)
from src.nodes.common.node_kit import build_resume_info, llm_json, maybe_interrupt_review, write_text_under_book
from src.state import State


def _build_summary_messages(state: State, config: RunnableConfig) -> list:
    plan: WriterPlan = state.get("writer_current_plan")
    cb = ContextBuilder()
    cb.header()
    cb.section(
        "角色设计任务",
        core_character_design_template.format(
            refined_book_setting=state.get("refined_book_setting", "暂无"),
            book_name=plan.book_name if plan else (state.get("book_name") or ""),
            book_description=(plan.book_description if plan else "") or "",
        ),
    )
    cb.json_schema(CharacterSummaryCollection.model_json_schema())
    demo = config.get("configurable", {}).get("demo_chapter")
    cb.demo(demo)
    budget = config.get("configurable", {}).get("prompt_budget_tokens")
    return cb.build(max_tokens=budget, policy="character")


def _build_detail_messages(*args, **kwargs):  # 兼容旧引用 - 保留占位以避免误用
    raise RuntimeError(
        "_build_detail_messages 已废弃，请使用 src/nodes/character/character_tasks.py::_build_detail_messages"
    )


async def _ensure_summaries(
    state: State, config: RunnableConfig
) -> tuple[Optional[CharacterSummaryCollection], dict, str]:
    summaries: Optional[CharacterSummaryCollection] = state.get("character_summaries")
    update: dict = {}
    next_node = "writer_character"

    if summaries is None or not getattr(summaries, "characters", None):
        messages = _build_summary_messages(state, config)
        summaries = await llm_json("writer_character:init", messages, CharacterSummaryCollection, config=config)

        # 落盘
        book_name = state.get("book_name") or (
            state.get("writer_current_plan").book_name if state.get("writer_current_plan") else ""
        )
        book_id = state.get("book_id")
        rel = "character/角色简介.md"
        review_file = write_text_under_book(book_name, book_id, rel, summaries.get_characters_content())

        update["character_summaries"] = summaries

        # interrupt 审核（一次）
        hr_update, maybe_next = await maybe_interrupt_review(
            state=state,
            config=config,
            node_name="writer_character",
            input_messages=messages,
            review_file_name=review_file,
            review_content=summaries.get_characters_content(),
            next_node_if_pass="writer_character",
        )
        if hr_update is not None:
            update.update(hr_update)
            next_node = maybe_next or next_node

    return summaries, update, next_node


async def _gen_detail_for_one(*args, **kwargs):  # 兼容旧引用 - 保留占位以避免误用
    raise RuntimeError("_gen_detail_for_one 已废弃，请使用 character_tasks 中的实现")


async def _ensure_details_concurrent(
    state: State, config: RunnableConfig, summaries: CharacterSummaryCollection
) -> tuple[Optional[CharacterDetailCollection], dict, str]:
    details: Optional[CharacterDetailCollection] = state.get("character_details")
    update: dict = {}
    next_node = "writer_outline"

    done_names = set([ch.name for ch in details.characters]) if details and details.characters else set()
    targets = [s.name for s in summaries.characters if s.name not in done_names]
    if not targets:
        return details, update, next_node

    # 使用 LangGraph task：spawn -> join
    cmd = await writer_character_spawn_tasks(state, targets)
    # 将 spawn 的指令返回给上层（通过 goto=writer_character_join）
    return details, {**update, **(cmd.update or {})}, "writer_character_join"


async def writer_character_fn(
    state: State, config: RunnableConfig
) -> Command[Literal["writer_outline", "writer_character"]]:
    # 0) 基本前置
    plan: WriterPlan | None = state.get("writer_current_plan")
    if not plan or not plan.is_all_step_completed() or not plan.is_all_step_refined():
        raise AssertionError("❌ 策划案未完成或未精炼，无法创建角色设计")

    # 1) 简介（缺则生成，一次 interrupt 审核）
    summaries, update1, next1 = await _ensure_summaries(state, config)
    update: dict = dict(update1)
    next_node = next1

    # 若被中断且未通过，直接返回等待继续
    if next_node == "writer_character" and not update.get("character_details"):
        # 保持停留在本节点，等待 join 或审阅通过
        pass

    if summaries is None or not getattr(summaries, "characters", None):
        return Command(update=update, goto=next_node)

    # 2) 详情（并发生成，整体一次 interrupt 审核）
    details, update2, next2 = await _ensure_details_concurrent(state, config, summaries)
    update.update(update2)
    next_node = next2

    # 3) resume_info
    update["resume_info"] = build_resume_info(
        state,
        node="writer_character",
        next_node=next_node,
        summary=f"角色并发生成 -> {next_node}",
        total=(len(summaries.characters) if summaries and getattr(summaries, "characters", None) else None),
        completed=(
            len(state.get("character_details").characters)
            if state.get("character_details") and getattr(state.get("character_details"), "characters", None)
            else None
        ),
    )

    return Command(update=update, goto=next_node)


__all__ = ["writer_character_fn"]
