from typing import Literal

from langgraph.config import RunnableConfig
from langgraph.types import Command

from src.context.context_builder import Con<PERSON><PERSON><PERSON><PERSON>
from src.nodes.book_setting.bs_steps_prompt import bs_refine_prompt_template
from src.nodes.common.book_types import WriterPlan, WriterPlanStep
from src.nodes.common.node_kit import build_resume_info, llm_text, maybe_interrupt_review, write_text_under_book
from src.nodes.common.store_manager import WriterStoreManager
from src.state import State


def _select_refine_target(state: State) -> tuple[int | None, WriterPlan | None, WriterPlanStep | None, str | None]:
    plan: WriterPlan | None = state.get("writer_current_plan")
    if plan is None:
        return None, None, None, None

    refine_id = None
    refine_step: WriterPlanStep | None = None
    refine_contents: str | None = None

    # 优先使用状态中的上下文，解决审核往返时的上下文一致性
    ctx = state.get("bs_refine_context")
    if ctx and isinstance(ctx, dict):
        try:
            refine_id = int(ctx.get("refine_id"))
            refine_contents = ctx.get("refine_contents")
            if 0 <= refine_id < len(plan.plan_steps):
                refine_step = plan.plan_steps[refine_id]
        except Exception:
            refine_id = None
            refine_step = None
            refine_contents = None

    # 没有上下文 → 选择第一个需要精炼的步骤
    if refine_step is None:
        try:
            refine_id, refine_step = next(
                (idx, step) for idx, step in enumerate(plan.plan_steps) if step.needs_refinement()
            )
            refine_contents = refine_step.get_step_content()
        except StopIteration:
            return None, plan, None, None

    return refine_id, plan, refine_step, refine_contents


def _build_messages(state: State, config: RunnableConfig, refine_contents: str) -> list:
    cb = ContextBuilder()
    cb.header()
    cb.section(
        "设定精炼任务",
        bs_refine_prompt_template.format(
            user_original_request=state.get("user_original_request"),
            refined_book_setting=state.get("refined_book_setting", "暂无"),
            new_contents=refine_contents,
        ),
    )
    budget = config.get("configurable", {}).get("prompt_budget_tokens")
    return cb.build(max_tokens=budget, policy="bs_steps")


async def bs_refine_fn(state: State, config: RunnableConfig) -> Command[Literal["bs_steps", "writer_character"]]:
    refine_id, plan, refine_step, refine_contents = _select_refine_target(state)

    # 无计划或无可精炼项 → 跳到角色设计
    if plan is None or refine_step is None:
        return Command(goto="writer_character")

    # 构建消息
    messages = _build_messages(state, config, refine_contents or "")

    # 生成文本
    content, _ = await llm_text("bs_refine", messages, config=config)

    # 计算写入的 refined_book_setting 文本（保持与旧实现一致）
    refined_book_setting = refine_contents if refine_id == 0 else content

    # 标记步骤为已精炼
    try:
        refine_step.set_status("refined")
    except Exception:
        pass

    # 入库
    try:
        if state.get("book_id"):
            sm = WriterStoreManager(book_id=state.get("book_id"))
            await sm.store_refined_book_setting(refined_book_setting)
    except RuntimeError as e:
        if "Called get_config outside of a runnable context" not in str(e):
            raise

    # 落盘
    file_rel = f"setting/0.{refine_id}-refined-booksetting.md"
    review_file = write_text_under_book(state.get("book_name"), state.get("book_id"), file_rel, refined_book_setting)

    # 如果所有步骤都已完成，清理上下文
    update: dict = {
        "refined_book_setting": refined_book_setting,
        "writer_current_plan": plan,
    }
    try:
        if plan.is_all_step_completed():
            update["bs_refine_context"] = None
        else:
            # 首次进入时写入上下文，便于审核往返
            if not state.get("bs_refine_context") and refine_contents is not None:
                update["bs_refine_context"] = {"refine_id": refine_id, "refine_contents": refine_contents}
    except Exception:
        pass

    # 计算下一跳
    next_node_if_pass = "writer_character" if plan.is_all_step_refined() else "bs_steps"

    # 人审（仅 interrupt；未启用则直接通过）
    hr_update, next_node = await maybe_interrupt_review(
        state=state,
        config=config,
        node_name="bs_refine",
        input_messages=messages,
        review_file_name=review_file,
        review_content=refined_book_setting,
        next_node_if_pass=next_node_if_pass,
    )
    if hr_update is None:
        hr_update, next_node = {}, next_node_if_pass
    update.update(hr_update)

    # resume_info
    update["resume_info"] = build_resume_info(
        state,
        node="bs_refine",
        next_node=next_node,
        summary=f"设定精炼：{refine_step.step_title if hasattr(refine_step, 'step_title') else ''} -> {next_node}",
        step_index=refine_id,
        step_title=(refine_step.step_title if hasattr(refine_step, "step_title") else None),
    )

    return Command(update=update, goto=next_node)


__all__ = ["bs_refine_fn"]
