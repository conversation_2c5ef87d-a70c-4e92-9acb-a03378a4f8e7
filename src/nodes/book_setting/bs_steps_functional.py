from typing import Literal

from langchain_core.messages import BaseMessage
from langgraph.config import RunnableConfig
from langgraph.types import Command

from src.context.context_builder import Context<PERSON>uilder
from src.nodes.book_setting.bs_planner_prompt import base_prompt_template
from src.nodes.book_setting.bs_steps_prompt import bs_step_execution_template
from src.nodes.common.book_types import WriterPlan, WriterPlanStep
from src.nodes.common.node_kit import build_resume_info, llm_text, maybe_interrupt_review, write_text_under_book
from src.nodes.common.store_manager import Writer<PERSON>toreManager
from src.state import State


def _select_step(
    state: State, config: RunnableConfig
) -> tuple[int | None, bool, WriterPlan | None, WriterPlanStep | None]:
    plan: WriterPlan | None = state.get("writer_current_plan")
    if not plan:
        return None, True, None, None

    if plan.plan_steps:
        try:
            step_index, is_last = plan.find_next_step()
            current_step = plan.plan_steps[step_index]
        except Exception:
            return 0, True, plan, None
    else:
        return 0, True, plan, None

    return step_index, is_last, plan, current_step


def _build_messages(state: State, config: RunnableConfig, plan: WriterPlan, step: WriterPlanStep) -> list[BaseMessage]:
    base_prompt = base_prompt_template.format()
    execution_prompt = bs_step_execution_template.format(
        user_original_request=state.get("user_original_request"),
        writer_current_plan=plan,
        current_step=step,
        refined_book_setting=state.get("refined_book_setting", "暂无"),
        unfinished_contents=plan.get_unrefined_contents(),
    )

    cb = ContextBuilder()
    cb.header()
    cb.section("设定步骤执行", base_prompt + execution_prompt)

    book_name = config.get("configurable", {}).get("book_name")
    if book_name is not None:
        cb.section("书名", f"书名使用《{book_name}》")

    demo_chapter = config.get("configurable", {}).get("demo_chapter")
    cb.demo(demo_chapter)

    demo_bs = config.get("configurable", {}).get("demo_bs")
    if demo_bs is not None and state.get("refined_book_setting", "暂无") != "暂无":
        cb.demo(demo_bs)

    budget = config.get("configurable", {}).get("prompt_budget_tokens")
    return cb.build(max_tokens=budget, policy="bs_steps")


async def bs_steps_fn(state: State, config: RunnableConfig) -> Command[Literal["bs_refine", "bs_steps"]]:
    # 前置检查
    step_index, is_last_step, plan, current_step = _select_step(state, config)
    if plan is None:
        return Command(goto="__end__")
    if plan.is_all_step_completed():
        return Command(goto="bs_refine")
    if step_index is None or current_step is None:
        return Command(goto="bs_refine")
    # 若上一步未精炼，跳转精炼
    if step_index > 0 and plan.plan_steps[step_index - 1].needs_refinement():
        return Command(goto="bs_refine")

    # 构建消息
    messages = _build_messages(state, config, plan, current_step)

    # 生成文本
    content, _ = await llm_text("bs_steps", messages, config=config)

    # 更新状态：写入当前步骤结果
    current_step.step_execution_res = content
    update: dict = {
        "writer_current_plan": plan,
        "last_executed_step_index": step_index,
    }

    # 落盘
    file_rel = f"setting/{step_index + 1}-{current_step.step_title}.md"
    review_file = write_text_under_book(state.get("book_name"), state.get("book_id"), file_rel, content)

    # 入库
    try:
        if state.get("book_id"):
            sm = WriterStoreManager(book_id=state.get("book_id"))
            await sm.store_step_result(step=current_step, result_content=content)
            await sm.store_writer_plan(plan=plan)
    except RuntimeError as e:
        # 非 runnable 上下文时跳过
        if "Called get_config outside of a runnable context" not in str(e):
            raise

    # 下一个节点
    next_node_if_pass = plan.get_next_node()

    # 人审（仅 interrupt；未启用则直接通过）
    hr_update, next_node = await maybe_interrupt_review(
        state=state,
        config=config,
        node_name="bs_steps",
        input_messages=messages,
        review_file_name=review_file,
        review_content=content,
        next_node_if_pass=next_node_if_pass,
    )
    if hr_update is None:
        # 未启用 interrupt 审核：直接进入后续节点
        hr_update, next_node = {}, next_node_if_pass
    update.update(hr_update)

    # resume_info
    update["resume_info"] = build_resume_info(
        state,
        node="bs_steps",
        next_node=next_node,
        summary=f"设定执行：{current_step.step_title} -> {next_node}",
        step_index=step_index,
        step_title=current_step.step_title,
        is_last_step=is_last_step,
    )

    return Command(update=update, goto=next_node)
