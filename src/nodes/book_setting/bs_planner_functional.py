from typing import Literal

from langchain_core.messages import BaseMessage

from langgraph.config import RunnableConfig
from langgraph.types import Command

from src.context.context_builder import ContextBuilder
from src.nodes.book_setting.bs_planner_prompt import base_prompt_template, plan_template
from src.nodes.common.book_types import WriterPlanSchema
from src.nodes.common.node_kit import (
    build_resume_info,
    llm_json,
    maybe_interrupt_review,
    save_plan_to_db,
    tier_of,
    write_text_under_book,
)
from src.state import State


def _build_messages(state: State, config: RunnableConfig) -> list[BaseMessage]:
    cb = ContextBuilder()
    cb.header()
    cb.section("创作计划任务", base_prompt_template.format())

    book_name = config.get("configurable", {}).get("book_name")
    if book_name is not None:
        cb.section("书名", f"书名使用《{book_name}》")

    cb.demo(config.get("configurable", {}).get("demo_chapter"))
    cb.demo(config.get("configurable", {}).get("demo_bs"))

    cb.section("计划格式", plan_template.format(max_step_num=5))
    cb.json_schema(WriterPlanSchema.model_json_schema())

    budget = config.get("configurable", {}).get("prompt_budget_tokens")
    messages = cb.build(max_tokens=budget, policy="bs_planner")
    messages.extend(state.get("messages", []))
    return messages


async def bs_planner_fn(state: State, config: RunnableConfig) -> Command[Literal["bs_steps"]]:
    # 1) 构建输入
    messages = _build_messages(state, config)

    # 2) 调用 LLM（JSON）
    parsed = await llm_json("bs_planner", messages, WriterPlanSchema, config=config)
    plan = parsed.convert_to_writer_plan()

    # 3) 写基础状态（book_id=thread_id, book_name）与用户原始诉求
    thread_id = config.get("configurable", {}).get("thread_id")
    if not thread_id:
        raise AssertionError("config.configurable.thread_id 未设置，无法作为 book_id")
    book_id = thread_id
    book_name = plan.book_name

    user_original_request = ""
    for msg in state.get("messages", []):
        if getattr(msg, "type", None) == "human":
            user_original_request = msg.content
            break

    update = {
        "book_name": book_name,
        "book_id": book_id,
        "writer_current_plan": plan,
        "user_original_request": user_original_request,
    }

    # 4) 落盘与入库
    file_rel = "setting/0-setting_plan.md"
    review_file = write_text_under_book(book_name, book_id, file_rel, plan.get_completed_contents())
    await save_plan_to_db(book_id, plan)

    # 5) 人审：优先 interrupt；未启用则直接进入后续节点
    hr_update, next_node = await maybe_interrupt_review(
        state=state,
        config=config,
        node_name="bs_planner",
        input_messages=messages,
        review_file_name=review_file,
        review_content=plan.get_completed_contents(),
        next_node_if_pass="bs_steps",
    )
    if hr_update is None:
        hr_update, next_node = {}, "bs_steps"
    update.update(hr_update)

    # 6) resume_info
    update["resume_info"] = build_resume_info(
        state,
        node="bs_planner",
        next_node=next_node,
        book_id=book_id,
        book_name=book_name,
        summary=f"策划案创建《{book_name}》 -> {next_node}",
        step_index=0,
        total_steps=len(plan.plan_steps or []),
    )

    return Command(update=update, goto=next_node)
