"""Define a custom Reasoning and Action agent.

Works with a chat model with tool calling support.
"""

import asyncio
from contextlib import asynccontextmanager

from langgraph.graph import StateGraph
from src.memory.store_utils import *
from src.nodes.book_setting.bs_planner_functional import bs_planner_fn
from src.nodes.book_setting.bs_refine_functional import bs_refine_fn
from src.nodes.book_setting.bs_steps_functional import bs_steps_fn
from src.nodes.chapter.chapter_segment_functional import chapter_segment_fn
from src.nodes.chapter.chapter_tasks import writer_chapter_store_join_node, writer_chapter_store_one_task
from src.nodes.chapter.stream_write_functional import stream_write_fn
from src.nodes.chapter.stream_write_tasks import writer_stream_post_join_node, writer_stream_post_one_task
from src.nodes.character.character_functional import writer_character_fn
from src.nodes.character.character_tasks import writer_character_detail_one_task, writer_character_join_node
from src.nodes.outlines.outline_functional import writer_outline_fn
from src.nodes.outlines.outline_tasks import writer_outline_join_node, writer_outline_refine_one_task

# 导入进度恢复相关模块
from src.nodes.progress.progress_functional import progress_check_fn
from src.nodes.scene.scene_functional import writer_scene_fn
from src.nodes.scene.scene_tasks import writer_scene_design_one_task, writer_scene_join_node
from src.runtime.runtime_context import RuntimeContext
from src.state import State

# 可选工具初始化：在缺少 mcps 模块时退化为 no-op
try:
    from src.tools.mcps.sequential_thinking import init_mcp_tools  # type: ignore
except Exception:  # ImportError 或运行时异常均退化

    async def init_mcp_tools():  # type: ignore
        return None


@asynccontextmanager
async def qflow_graph():
    """
    创建并返回QFlow主图的异步上下文管理器
    """
    async with get_store_and_checkpointer() as (store, checkpointer):
        # 研究团队
        init_mcp_task = asyncio.create_task(init_mcp_tools())

        writer_team_graph = (
            StateGraph(State, context_schema=RuntimeContext)
            .set_entry_point("progress_check")  # 设置进度检查为入口点
            .add_node("progress_check", progress_check_fn)  # 添加进度检查节点（函数式）
            .add_node("bs_planner", bs_planner_fn)
            .add_node("bs_steps", bs_steps_fn)
            .add_node("bs_refine", bs_refine_fn)
            .add_node("writer_character", writer_character_fn)  # 角色设计节点（函数式，并发版）
            # 角色并发：子任务与聚合
            .add_node("writer_character_detail_one", writer_character_detail_one_task)
            .add_node("writer_character_join", writer_character_join_node)
            .add_node("writer_outline", writer_outline_fn)  # 添加大纲节点（函数式）
            # 大纲并发：子任务与聚合节点
            .add_node("writer_outline_refine_one", writer_outline_refine_one_task)
            .add_node("writer_outline_join", writer_outline_join_node)
            .add_node("writer_scene", writer_scene_fn)  # 添加场景设计节点（函数式）
            # 场景并发：子任务与聚合
            .add_node("writer_scene_design_one", writer_scene_design_one_task)
            .add_node("writer_scene_join", writer_scene_join_node)
            .add_node("stream_write", stream_write_fn)
            # 流式后处理：单元任务与汇总
            .add_node("writer_stream_post_one", writer_stream_post_one_task)
            .add_node("writer_stream_post_join", writer_stream_post_join_node)
            .add_node("chapter_segment", chapter_segment_fn)
            # 章节入库（可选：用于后续扩展 spawn/join 并行入库）
            .add_node("writer_chapter_store_one", writer_chapter_store_one_task)
            .add_node("writer_chapter_store_join", writer_chapter_store_join_node)
            .set_finish_point("chapter_segment")  # 设置分章节点为结束点
            .compile(name="QFlowAgent", checkpointer=checkpointer, store=store)
        )

        await init_mcp_task
        yield writer_team_graph


"""
    docker run --name qflow_agent_db \
        -e POSTGRES_USER=postgres \
        -e POSTGRES_PASSWORD=postgres \
        -e POSTGRES_DB=qflow \
        -d -p 5432:5432 postgres
# usage:
# graph = builder.compile(checkpointer=checkpointer, store=store)
# store.aput(namespace, str(uuid.uuid4()), {"data": memory})
# store.asearch(namespace, query=query)
"""
