"""Define the state structures for the agent."""

from __future__ import annotations

import base64

import lzma

from typing import Any, TypedDict

from langchain_core.load.dump import dumps

from langchain_core.load.load import loads

from langchain_core.messages import AnyMessage

from langgraph.graph import add_messages

from typing_extensions import Annotated

from src.nodes.common.book_types import (
    APP_SERIALIZABLE_MAPPINGS,
    BookDetail,
    BookSceneDesign,
    CharacterDetailCollection,
    CharacterSummaryCollection,
    HumanReviewParams,
    WriterPlan,
)

from src.memory.store_utils import *


class State(TypedDict):
    """State for the agent system, extends MessagesState with next field."""

    messages: Annotated[list[AnyMessage], add_messages]

    think: str = ""

    user_original_request: str = ""
    writer_current_plan: WriterPlan = None
    refined_book_setting: str
    refined_book_outlines: str
    book_id: str = None
    book_name: str = None
    book_detail: BookDetail = None
    # 场景设计（并发生成/更新）
    scene_design: BookSceneDesign = None
    # 角色设计 - 分离的角色信息
    character_summaries: CharacterSummaryCollection = None
    character_details: CharacterDetailCollection = None

    # 人工审核相关（interrupt-only，v2不再使用旧参数，保留类型以兼容反序列化）
    human_review_params: HumanReviewParams = None
    # 跨轮 /skip 策略剩余计数（由 NodeKit.maybe_interrupt_review 维护）
    skip_counters: dict[str, int]

    # 恢复用的富提示历史（按顺序追加，无上限）。每个函数式节点在成功执行后写入。
    resume_info: list[dict[str, Any]]

    # -------------------- 流式后处理（join 限流/队列观测） --------------------
    # 由 stream_write_fn 派发分块并在 writer_stream_post_join_node 聚合与限流调度
    stream_post_batch_id: str
    stream_post_chunks: list[str]
    stream_post_expected_indices: list[int]
    stream_post_pending_indices: list[int]
    stream_post_dispatched_indices: list[int]
    stream_post_dispatch_times: dict
    stream_post_requeue_counts: dict
    # 在飞观测（CLI 显示）
    stream_post_inflight_count: int
    stream_post_inflight_preview: list[int]

    # -------------------- 章节入库（join 限流/队列观测） --------------------
    # 由 chapter_segment_fn 维护期望集合，writer_chapter_store_join_node 调度
    chapter_store_expected_numbers: list[int]
    chapter_store_pending_numbers: list[int]
    chapter_store_dispatched_numbers: list[int]
    chapter_store_dispatch_times: dict
    chapter_store_requeue_counts: dict
    # 在飞观测（CLI 显示）
    chapter_store_inflight_count: int
    chapter_store_inflight_preview: list[int]

    @classmethod
    def deserialize_state(self, serialize_content: str) -> dict:
        # 检查是压缩b64还是原始json
        if serialize_content.startswith("B"):
            compressed_content = base64.b64decode(serialize_content[1:])
            decompressed_content = lzma.decompress(compressed_content).decode("utf-8")
            dump_content = decompressed_content
        else:
            dump_content = serialize_content[1:] if serialize_content.startswith("J") else serialize_content

        # 使用 LangChain 的 loads 进行反序列化，添加自定义命名空间
        state = loads(
            dump_content,
            valid_namespaces=["src"],
            additional_import_mappings=APP_SERIALIZABLE_MAPPINGS,
        )
        return state

    @classmethod
    def serialize_state(self, state: State) -> str:
        # 使用 LangChain 的 dumps 进行序列化
        dump_content = dumps(state)
        # lzma压缩
        compressed_content = lzma.compress(dump_content.encode("utf-8"), preset=9)
        # db需要str类型，做b64处理
        base64_content = base64.b64encode(compressed_content).decode("utf-8")

        # 如果压缩后更长则直接使用原始json
        if len(base64_content) < len(dump_content):
            serialize_content = "B" + base64_content
        else:
            serialize_content = "J" + dump_content
        return serialize_content
