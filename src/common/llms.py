import json
import logging
import os
from pathlib import Path
from typing import <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>

from langchain.chat_models import init_chat_model
from langchain_community.embeddings import DashScopeEmbeddings
from langchain_core.language_models import BaseChatModel
from src.common.llm_config import get_model_for_request, ModelTier, RequestMode
from src.common.models.model_list import get_llm_params

# values
logger = logging.getLogger(__name__)


def get_llm_by_model_name(model_name: str, params: dict[str, Any] = {}) -> BaseChatModel:
    llm_params = get_llm_params(model_name)
    assert isinstance(llm_params, dict)

    merge_params = {**llm_params, **params}  # 如果有相同的key，后者覆盖前者

    if model_name.startswith("deepseek"):
        from langchain_deepseek import ChatDeepSeek

        merge_params.pop("model_provider")
        merge_params["api_base"] = merge_params["base_url"]
        merge_params["max_tokens"] = 64000
        base_chat_model = ChatDeepSeek(**merge_params)
    else:
        base_chat_model = init_chat_model(**merge_params)

    return base_chat_model


def get_default_embedding_model():
    ali_embedding_model = DashScopeEmbeddings(
        model="text-embedding-v4",
        dashscope_api_key=os.getenv("ALI_API_KEY"),
    )
    return ali_embedding_model
