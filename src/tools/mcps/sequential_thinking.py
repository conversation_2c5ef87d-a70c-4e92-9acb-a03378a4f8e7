import logging


logger = logging.getLogger(__name__)


async def init_mcp_tools() -> None:
    """Placeholder MCP tools initializer.

    当前仓库未提供实际 MCP 工具实现；为了确保 `qflow_graph()` 可导入运行，
    这里提供一个无副作用的占位版本。后续可替换为真实实现或在图中移除此依赖。
    """
    try:
        logger.info("init_mcp_tools(): placeholder no-op initialized")
    except Exception as e:
        logger.warning(f"init_mcp_tools() placeholder encountered error: {e}")
        # 不抛出异常，保证主流程可继续
        return None
