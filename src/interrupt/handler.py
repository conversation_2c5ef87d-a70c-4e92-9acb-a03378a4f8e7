from typing import Any, Dict, Optional, Tuple


class InterruptProcessor:
    """处理中断相关逻辑的类"""

    @staticmethod
    def extract_interrupt_info(chunk: Dict[str, Any]) -> Tuple[Any, Any]:
        """从chunk中提取中断信息"""
        interrupts = chunk.get("__interrupt__") or []
        interrupt_item = InterruptProcessor._get_first_interrupt(interrupts)
        payload = InterruptProcessor._extract_payload(interrupt_item)
        return interrupt_item, payload

    @staticmethod
    def validate_response(resp: Dict[str, Any]) -> bool:
        """验证用户响应是否有效"""
        # 基本验证 - 确保响应不是空的
        if not resp:
            return False

        # 检查 skip 操作是否包含必要的参数
        if resp.get("action") == "skip":
            return resp.get("domain") is not None

        # 对于其他操作，我们接受任何输入
        return True

    @staticmethod
    def parse_user_input(user_input: str) -> Dict[str, Any]:
        """解析用户输入"""
        # 移植自 src.nodes.common.node_kit.parse_human_review_input
        user_input = user_input.strip()
        if not user_input:
            return {"action": "respond", "content": ""}

        # 处理 pass 命令
        if user_input == "/pass":
            return {"action": "pass"}

        # 处理 skip 命令
        if user_input.startswith("/skip"):
            parts = user_input.split()
            if len(parts) >= 2:
                domain = parts[1]
                count = parts[2] if len(parts) > 2 else "1"
                return {"action": "skip", "domain": domain, "count": count}
            else:
                # skip 命令格式不正确
                return {"action": "skip"}

        # 处理切换模型命令
        if user_input.startswith(("/high", "/medium", "/low")):
            parts = user_input.split(" ", 1)
            priority = parts[0][1:]  # 去掉斜杠
            comment = parts[1] if len(parts) > 1 else ""
            return {"action": "review", "priority": priority, "comment": comment}

        # 默认为直接响应
        return {"action": "respond", "content": user_input}

    @staticmethod
    def _get_first_interrupt(interrupts: Any) -> Any:
        """获取第一个中断项"""
        try:
            return interrupts[0] if isinstance(interrupts, (list, tuple)) and interrupts else None
        except Exception:
            return None

    @staticmethod
    def _extract_payload(interrupt_item: Any) -> Any:
        """从中断项中提取 payload"""
        if interrupt_item is None:
            return None

        payload = getattr(interrupt_item, "value", None)
        if payload is None:
            payload = getattr(interrupt_item, "payload", None)
        return payload
