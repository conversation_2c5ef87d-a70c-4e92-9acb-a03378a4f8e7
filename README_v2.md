## QFlowAgent v2 变更说明（完成版）

本文件面向使用者与维护者，系统性说明 v2 的架构升级、功能变化、废弃项与后续维护建议。与实现代码、脚本及测试已对齐，可直接作为迁移与使用指南。

---

### 为什么是 v2
- 函数式节点 + LangGraph 原生：移除厚重基类，节点以 `async def node(state, config) -> Command` 统一返回更新与跳转。
- 审核 interrupt-only：通过 LangGraph `interrupt` 与 CLI 交互，不再存在独立人审节点及兼容路径。
- 任务化并发：大纲/角色/场景/流式后处理与章节入库采用 `spawn/join`，支持限流、requeue、队列观测与可重入。
- 统一 Streaming 与可观测性：Runner 使用 `astream_events(...)`，CLI 统一渲染 `on_*` 事件。
- 数据层 v2-only：命名空间统一为 `("writer_app_v2", book_id, ...)` 且强制 `schema_version=2`，不再保留 v1 兼容代码。

---

### 关键变更（与代码一致）
- 主图与节点
  - 入口：`progress_check`（函数式，支持历史恢复与新建）
    - 若 CLI 已传入 `config.configurable.thread_id`（以及可选 `checkpoint_id`）且 `default_new_project=False`，则直接按该线程（与指定 checkpoint）恢复，跳过交互扫描。
    - 仅当未指定线程时，才会进行一次交互式扫描与选择。
  - 设定：`bs_planner_fn` → `bs_steps_fn` → `bs_refine_fn`
  - 角色：`writer_character_fn`（并发生成详情，join 汇总，一次 interrupt 审核）
  - 大纲：`writer_outline_fn`（初始化失败→回退重试→占位草稿+interrupt；并发细化 join）
  - 场景：`writer_scene_fn`（并发设计 join）
  - 写作：`stream_write_fn`（触发流式后处理 spawn/join → `chapter_segment_fn`）
  - 章节入库：`writer_chapter_store_one/join`（join 限流、requeue）

- 并发与限流（队列观测一致）
  - 流式后处理 join：`writer_stream_post_join_node`
    - 读取 `stream_post_max_concurrency`、`*_requeue_ttl_ms/*_requeue_max`；
    - 维护 `stream_post_pending_indices/stream_post_dispatched_indices/stream_post_inflight_*`；
    - 生成 `chapters_stream/batch-<id>/批量-流式后处理.md`。
  - 章节入库 join：`writer_chapter_store_join_node`
    - 读取 `chapter_store_max_concurrency`、`*_requeue_ttl_ms/*_requeue_max`；
    - 维护 `chapter_store_pending_numbers/chapter_store_dispatched_numbers/.../chapter_store_inflight_*`；
    - 保留 resume_info 统计。
  - 在飞明细预览长度：统一由 `config.configurable.queue_preview_len` 控制（默认 5）。

- 审核与 CLI
  - `NodeKit.maybe_interrupt_review()` 触发 `interrupt`；CLI 解析 `/pass`、`/skip [domain] [n]`、`/high|/medium|/low [意见]` 或文本意见。
  - `/skip` 策略写入 `config.configurable.skip_policies`，剩余次数持久在 `state.skip_counters`。
  - CLI 打印事件到终端或用户自定义重定向。

- 数据与命名空间（v2-only）
  - 统一写入 `schema_version=2`；命名空间 `("writer_app_v2", book_id, data_type)`；
  - `WriterStoreManager` 提供 `plan/settings/outline/scene/chapters/stream` 的存取与分块向量化；
  - 幂等键规范：章节全文 `chapter_{N}_{title}_full`，分块 `chapter_{N}_{title}_chunk_{i}`；流式分块 `batch_{batch_id}_chunk_{i:03d}`。

- 稳健性
  - 结构化：`NodeKit.llm_json` 内置 Schema 回显检测与一次回退重试；
  - 文本：`NodeKit.llm_text` 支持最小长度/敏感模式校验，一次回退重试与可选 tier 升级。

---

### 新功能清单
- requeue 与降级占位：join 节点对超时在飞任务进行重派发；超过阈值写入占位结果 `error: "requeue_exceeded"`，确保 join 可收敛。
 交互运行：`uv run apps/main.py`；如需关闭审核以快速生成，可使用 `uv run apps/main.py --no-review`（默认递归限制 100）。可选通过 `--book-name` 指定书名，不传则由策划阶段自动确定。
- 历史恢复：`CheckpointManager` 支持按 `book_id` 聚合查看、树形选择回退、获取最新状态并恢复。

---

### 废弃与移除
- v1 数据层与示例：移除 `writer_app` 命名空间与 `schema_version=1` 的任何读写/脚本/文档。
- 历史类节点与兼容层：移除 `HumanReviewNode`、`FlowControlNode`、`StructuredDataNode`、`MultiStageNode`、`IterativeProcessingNode`；不再保留 `maybe_set_human_review` 与自定义流打印回调。
- 统一 API：已移除 `get_response_from_astream(..., callback)` 的 callback 形参及相关调用点。

---

### 已验证与测试
- 单测：`unitest/test_join_nodes.py` 覆盖 join 在飞预览长度、requeue 超阈占位、边界输入稳健性等；全部通过。
- 端到端：`uv run apps/main.py`（交互）与 `uv run apps/main.py --no-review`（非交互）正常。
- 工具链：交互式恢复/删除集成于 `uv run apps/main.py`。

---

### 待办与维护建议
- 轻微遗留（已完成）：已将 `kStateRestoreNamespace_commit_state/head` 常量抽取到 `src/checkpoint/constants.py`，并更新了所有引用；已删除历史 `progress_manager.py` 文件。
- 清理：删除空/废弃目录 `src/nodes/human_review/`、`src/nodes/chatper/`（拼写残留）、`src/nodes/store/`、`src/context_manager/`、`src/config/`，不再保留兼容路径。
- 文档对齐：若后续新增配置项（如更多限流阈值/观测项），同步更新 `README_nodes.md` 与本文件。

---

### 运行与使用
- 统一启动：`uv run apps/main.py`
- 语义检索：`uv run apps/vector_query.py --book-id <book_id>`
- 重试脚本：`uv run apps/retry_failed_store.py book_name/book_id --dry-run`
- （已移除）队列统计脚本
- 单测：`uv run pytest -q unitest`

---

### 参考
- 主图：`src/graph/graph.py`
- 工具层：`src/nodes/common/node_kit.py`
- 任务与并发：`src/nodes/**/{outline,scene,character,chapter}*`
- 存储：`src/nodes/common/store_manager.py`
- Checkpoint：`src/checkpoint/checkpoint_manager.py`、`README_checkpoint.md`
