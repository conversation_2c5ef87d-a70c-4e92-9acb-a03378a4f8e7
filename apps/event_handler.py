from typing import Any, Callable, Dict, List, Optional

from langchain_core.messages import BaseMessage
from langgraph.graph.state import CompiledStateGraph
from langgraph.types import Command

from src.interrupt import InterruptProcessor
from src.nodes.common.node_kit import apply_skip_policy


class EventHandler:
    def __init__(
        self,
        *,
        graph: CompiledStateGraph,
        config: Dict[str, Any],
        input_func: Callable[[str], str] = input,
    ) -> None:
        self.graph = graph
        self.config = config
        self.input_func = input_func

        cfg = config.get("configurable") or {}
        try:
            self.preview_len = int(cfg.get("max_preview_len", 400))
        except (ValueError, TypeError):
            self.preview_len = 400

        self._start_times: Dict[str, float] = {}

        # 友好名称映射
        self._FRIENDLY_NAME_MAP: Dict[str, str] = {
            # 节点
            "progress_check": "进度检查",
            "bs_planner": "策划",
            "bs_steps": "设定执行",
            "bs_refine": "设定精炼",
            "writer_character": "角色",
            "writer_character_detail_one": "角色详情-单人",
            "writer_character_join": "角色详情-汇总",
            "writer_outline": "大纲",
            "writer_outline_refine_one": "大纲细化-单卷",
            "writer_outline_join": "大纲细化-汇总",
            "writer_scene": "场景",
            "writer_scene_design_one": "场景设计-单卷",
            "writer_scene_join": "场景设计-汇总",
            "stream_write": "流式写作",
            "chapter_segment": "智能分章",
            "writer_chapter_store_one": "章节入库-单元",
            "writer_chapter_store_join": "章节入库-汇总",
            # 任务
            "writer_stream_post_one": "流式后处理-单元",
            "writer_stream_post_join": "流式后处理-汇总",
        }

    # -------------------- 工具方法 --------------------

    def _friendly_name(self, name: Optional[str]) -> str:
        """获取节点的友好显示名称"""
        if not name:
            return "(未命名)"
        return self._FRIENDLY_NAME_MAP.get(name, name)

    def _pretty_event_header(self, title: str) -> None:
        """打印格式化的事件标题"""
        print(f"\n{title}")
        print("=" * len(title))

    def _print_kv(self, label: str, data: Dict[str, Any], max_value_len: Optional[int] = None) -> None:
        """安全地打印键值对，限制值的长度"""
        try:
            limit = self.preview_len if max_value_len is None else max_value_len
            safe = {k: (str(v)[:limit] + ("..." if len(str(v)) > limit else "")) for k, v in data.items()}
            print(f"{label}:", safe)
        except Exception:
            # 静默失败，避免影响主流程
            pass

    def _safe_preview_text(self, text: Any, limit: int = 200) -> str:
        """安全地截取文本预览"""
        try:
            s = str(text)
            return s[:limit] + ("..." if len(s) > limit else "")
        except Exception:
            return "-"

    def _display_interrupt_info(self, chunk: Dict[str, Any]) -> None:
        """显示中断信息"""
        self._pretty_event_header("[interrupt] 需要审核/输入")

        # 使用 src 中的处理器提取中断信息
        _, payload = InterruptProcessor.extract_interrupt_info(chunk)

        content = ""
        if isinstance(payload, dict):
            # 显示 payload 键值对
            safe_payload = {k: self._safe_preview_text(v, self.preview_len) for k, v in payload.items()}
            self._print_kv("payload", safe_payload, max_value_len=self.preview_len)

            # 提取内容
            content = payload.get("content") or payload.get("text") or ""
        else:
            content = str(payload) if payload is not None else ""

        # 显示内容预览
        if content:
            print("---- 待审内容预览（截断） ----")
            lim = min(5000, self.preview_len)
            print(content[:lim] + ("..." if len(content) > lim else ""))

    def _interact_with_user_until_valid(self) -> Dict[str, Any]:
        """与用户交互直到获得有效输入"""
        while True:
            user_input = self.input_func(
                "输入 '/pass' 通过；或 '/skip [domain] [n]' 跳过；或 '/high|/medium|/low [意见]'；或直接输入建议："
            ).strip()

            # 使用 src 中的处理器解析输入
            resp = InterruptProcessor.parse_user_input(user_input)

            # 使用 src 中的处理器验证输入
            if InterruptProcessor.validate_response(resp):
                # 应用跳过策略（如果适用）
                self._apply_skip_policy_from_response(resp)
                return resp

            print("输入无效，请重新输入。")

    # -------------------- 核心逻辑 --------------------

    async def run(self, input_messages: Optional[List[BaseMessage]]) -> None:
        async for chunk in self.graph.astream(input_messages, self.config, stream_mode=["updates"]):
            assert isinstance(chunk, tuple)
            event_type, event_value = chunk
            if event_type == "updates":
                if "__interrupt__" in event_value:
                    await self._handle_interrupt_chunk(event_value)
                else:
                    await self._handle_regular_chunk(event_value)
            else:
                raise ValueError(f"Unknown event type: {event_type}")

    def _apply_skip_policy_from_response(self, resp: Dict[str, Any]) -> None:
        """根据用户响应应用跳过策略"""
        if resp.get("action") == "skip" and (resp.get("domain") is not None):
            try:
                domain = resp.get("domain")
                count = int(resp.get("count", 1))
                apply_skip_policy(self.config, domain, count)
                print(f"已设置跳过策略：domain={domain} count={count}")
            except Exception as e:
                print(f"设置跳过策略时出错: {e}")

    async def _handle_regular_chunk(self, chunk: Any) -> None:
        """处理常规更新块"""
        if isinstance(chunk, dict):
            keys = list(chunk.keys())
            if keys:
                print("更新字段:", keys)
        else:
            text = str(chunk)
            lim = min(500, self.preview_len)
            print(text[:lim] + ("..." if len(text) > lim else ""))

    async def _handle_interrupt_chunk(self, chunk: Any) -> None:
        # 显示中断信息
        self._display_interrupt_info(chunk)

        # 与用户交互直到获得有效输入
        resp = self._interact_with_user_until_valid()

        next_input = Command(resume=resp)
        return await self.run(next_input)
