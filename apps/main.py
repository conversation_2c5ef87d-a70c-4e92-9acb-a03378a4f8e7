#!/usr/bin/env python3
"""
🚀 QFlowAgent 统一启动器

这是QFlowAgent的主入口文件，集成了所有功能和配置选项。
用户可以通过修改下面的配置来控制应用行为。

使用方法：
    uv run apps/main.py

功能特性：
- 📋 统一配置管理，所有选项都有详细注释
- 🔄 支持checkpoint恢复和项目管理
- 🎯 支持指定book_id直接恢复
- 🎮 支持交互模式
- 📊 完整的事件流和进度跟踪
"""

import argparse
import asyncio
import logging

import uuid
from pathlib import Path
from typing import Optional

from cli_events import stream_graph_events
from langchain_core.messages import HumanMessage
from langgraph.errors import GraphRecursionError
from src.checkpoint import BookSelectionResult, CheckpointManager
from src.graph.graph import qflow_graph
from src.init_log import init_log

# =============================================================================
# 📋 基础配置区域
# =============================================================================

# 工作区路径
WORKSPACE_PATH = Path("./workspace")

# 是否启用人工审核中断（True=交互模式，False=自动模式）
ENABLE_INTERRUPT_REVIEW = True

# 写作章节数量（建议1-3章，过多可能影响性能）
WRITE_CHAPTERS_CNT = 2

# 是否为新项目（True=创建新项目，False=恢复现有项目）
DEFAULT_NEW_PROJECT = True

# 数据库为空时的行为：现已固定为自动创建新项目（移除可配置项）

# =============================================================================
# 🔄 恢复配置区域
# =============================================================================

# 指定book_id直接恢复功能
# 取消注释下面的行并填入book_id来启用直接恢复功能
# 启用后将跳过交互式选择，直接恢复指定项目
RESUME_BOOK_ID: Optional[str] = "1cae2a3d-c16c-48b8-9b51-b50fd956e14c"

# 指定thread_id恢复（可选，通常自动选择最新的）
# RESUME_THREAD_ID = "specific-thread-id"
RESUME_THREAD_ID: Optional[str] = None

# 指定checkpoint_id回退（可选，用于回退到历史状态）
# RESUME_CHECKPOINT_ID = "specific-checkpoint-id"
RESUME_CHECKPOINT_ID: Optional[str] = None

# =============================================================================
# 🎯 运行模式配置
# =============================================================================

# =============================================================================
# 📊 显示和界面配置
# =============================================================================

# 显示模式（"keys"=显示关键信息，"full"=显示完整信息）
DISPLAY_MODE = "keys"

# 最大预览长度（字符数）
MAX_PREVIEW_LEN = 400

# 流式预览长度（字符数）
STREAM_PREVIEW_LEN = 120

# =============================================================================
# 🤖 LLM和文本质量配置
# =============================================================================

# LLM生成文本的最小长度要求
LLM_TEXT_MIN_LENGTH = 200

# LLM文本敏感模式检测（检测占位符等问题文本）
LLM_TEXT_SENSITIVE_PATTERNS = [r"(?i)\b(placeholder|todo)\b"]

# LLM重试等级（"low"/"medium"/"high"）
LLM_TEXT_RETRY_TIER = "high"

# =============================================================================
# 📚 项目和流程配置
# =============================================================================

# 可选：通过 CLI 提供书名；不提供时由策划阶段自动确定
CLI_BOOK_NAME: Optional[str] = None

# 递归限制（防止无限循环）
RECURSION_LIMIT = 100

# 默认人类消息（用于新项目启动）
DEFAULT_HUMAN_MSG = """
请策划一部玄幻爽文小说, 包括宏大的世界观、修炼成长体系与法则、完善的战斗逻辑、有特色看点亮点等。
- 修炼体系不喜欢金丹筑基类的，倾向于偏魔法玄幻。
- 主角掌控极其罕见的空间与时间之力，其他角色大多是普通的魔法体系。
- 世界观宏大，史诗感强，有深度，伏笔设计精彩。
- 整体采用爽文发展，扮猪吃虎等各种爽点，不要出现主角被虐的情况
- 不要搞熵增熵减、熵值、系统管理员等这种大众看不懂的，或者脱离主流小说的奇怪设定
"""

# =============================================================================
# 🔧 高级配置（一般不需要修改）
# =============================================================================


# =============================================================================
# 🚀 主程序逻辑
# =============================================================================


class RunPlan(dict):
    pass


async def build_run_plan() -> RunPlan:
    """
    统一产出一次运行计划：{mode: 'new'|'resume', thread_id, checkpoint_id?, book_id?, llm_input?}
    - 前置：清理“无策划案”的历史记录
    - 若设置 RESUME_BOOK_ID -> 直接恢复
    - 否则进入交互选择（含删除流程），直至返回 new/resume 的计划
    """
    async with CheckpointManager.connect() as mgr:
        # 启动时清理“无策划案”的历史记录
        try:
            deleted = await mgr.cleanup_checkpoints_without_plan(dry_run=False)
            if deleted > 0:
                print(f"🧹 已清理无策划案历史的线程数：{deleted}")
        except Exception:
            pass

        # 直接恢复路径
        if RESUME_BOOK_ID:
            print(f"\n🔄 直接恢复模式：book_id={RESUME_BOOK_ID}")
            thread_id = RESUME_THREAD_ID
            checkpoint_id = RESUME_CHECKPOINT_ID
            if not thread_id:
                try:
                    checkpoints = await mgr.list_checkpoints(RESUME_BOOK_ID or "")
                    if checkpoints:
                        latest = checkpoints[0]
                        thread_id = latest.thread_id or RESUME_BOOK_ID
                    else:
                        thread_id = RESUME_BOOK_ID
                except Exception:
                    thread_id = RESUME_BOOK_ID
            print(f"📖 恢复项目：book_id={RESUME_BOOK_ID}，thread_id={thread_id}")
            if checkpoint_id:
                print(f"⏪ 回退到checkpoint：{checkpoint_id}")
            return RunPlan(mode="resume", thread_id=thread_id, checkpoint_id=checkpoint_id, book_id=RESUME_BOOK_ID)

        # 交互选择路径
        print("\n🎮 交互式选择模式")
        while True:
            result: BookSelectionResult = await mgr.prompt_select_book()

            if result.action in {"new", "empty"}:
                thread_id = f"{uuid.uuid4()}"
                print(f"🆕 创建新项目：thread_id={thread_id}")
                llm_input = {"messages": [HumanMessage(content=DEFAULT_HUMAN_MSG)]}
                return RunPlan(mode="new", thread_id=thread_id, llm_input=llm_input)

            if result.action == "delete":
                try:
                    ans = input("是否先进行 dry-run 预览将被删除的资源清单？(y/N): ").strip().lower()
                except KeyboardInterrupt:
                    ans = "n"
                if ans == "y":
                    await mgr.delete_book(result.book_id, workspace_path=WORKSPACE_PATH, dry_run=True)
                    try:
                        confirm = input("\n是否继续执行实际删除？(y/N): ").strip().lower()
                    except KeyboardInterrupt:
                        confirm = "n"
                    if confirm != "y":
                        print("已取消删除，返回主菜单。")
                        continue
                await mgr.delete_book(result.book_id, workspace_path=WORKSPACE_PATH, dry_run=False)
                print(f"✅ 已删除 book_id={result.book_id} 的全部数据")
                continue

            if result.action == "resume":
                if result.checkpoint_id:
                    print(
                        f"\n⏪ 回退并恢复项目：book_id={result.book_id}，thread_id={result.thread_id}，"
                        f"checkpoint_id={result.checkpoint_id}，书名={result.book_name or '(未命名)'}\n"
                    )
                    return RunPlan(
                        mode="resume",
                        thread_id=result.thread_id,
                        checkpoint_id=result.checkpoint_id,
                        book_id=result.book_id,
                    )
                else:
                    print(
                        f"\n🔄 恢复项目：book_id={result.book_id}，thread_id={result.thread_id}，"
                        f"书名={result.book_name or '(未命名)'}\n"
                    )
                    return RunPlan(mode="resume", thread_id=result.thread_id, book_id=result.book_id)

            # 未知 action：回退为新建
            thread_id = f"{uuid.uuid4()}"
            print(f"🆕 创建新项目：thread_id={thread_id}")
            llm_input = {"messages": [HumanMessage(content=DEFAULT_HUMAN_MSG)]}
            return RunPlan(mode="new", thread_id=thread_id, llm_input=llm_input)


async def run_qflow(plan: RunPlan) -> None:
    """
    单一入口：构建配置 -> 调用 qflow_graph
    - 处理 GraphRecursionError：若可获取 book_id，则允许回退并重试（从同一入口调用）。
    """
    while True:
        default_new_project = plan.get("mode") == "new"
        config = build_config(
            thread_id=plan["thread_id"],
            default_new_project=default_new_project,
            checkpoint_id=plan.get("checkpoint_id"),
        )
        llm_input = plan.get("llm_input") if default_new_project else None

        async with qflow_graph() as graph:
            await stream_graph_events(graph, llm_input, config)
        return


def build_config(thread_id: str, default_new_project: bool, checkpoint_id: Optional[str] = None) -> dict:
    """构建配置字典"""
    # 重要：LangGraph 的 recursion_limit 需要位于 config 顶层，而不是 configurable 下
    config = {
        "recursion_limit": RECURSION_LIMIT,
        "configurable": {
            "thread_id": thread_id,
            "workspace_path": WORKSPACE_PATH,
            "enable_interrupt_review": ENABLE_INTERRUPT_REVIEW,
            "write_chapters_cnt": WRITE_CHAPTERS_CNT,
            "default_new_project": default_new_project,
            "display_mode": DISPLAY_MODE,
            "max_preview_len": MAX_PREVIEW_LEN,
            "stream_preview_len": STREAM_PREVIEW_LEN,
            "llm_text_min_length": LLM_TEXT_MIN_LENGTH,
            "llm_text_sensitive_patterns": LLM_TEXT_SENSITIVE_PATTERNS,
            "llm_text_retry_tier": LLM_TEXT_RETRY_TIER,
        },
    }

    # 仅当通过 CLI 显式提供书名时注入；否则由 bs_planner 在计划阶段确定
    if CLI_BOOK_NAME:
        config["configurable"]["book_name"] = CLI_BOOK_NAME

    # 添加checkpoint_id（如果指定）
    if checkpoint_id:
        config["configurable"]["checkpoint_id"] = checkpoint_id

    return config


# =========================== CLI 参数解析 ===========================


def parse_args() -> argparse.Namespace:
    parser = argparse.ArgumentParser(
        description="QFlowAgent 统一启动器",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )
    # 基础
    parser.add_argument("--workspace", dest="workspace", type=Path, default=WORKSPACE_PATH, help="工作区路径")
    parser.add_argument(
        "--chapters", dest="write_chapters_cnt", type=int, default=WRITE_CHAPTERS_CNT, help="写作章节数量"
    )
    parser.add_argument("--review", dest="enable_interrupt_review", action="store_true", help="启用人工审核中断")
    parser.add_argument("--no-review", dest="enable_interrupt_review", action="store_false", help="禁用人工审核中断")
    parser.set_defaults(enable_interrupt_review=ENABLE_INTERRUPT_REVIEW)

    # 恢复/回退
    parser.add_argument("--resume-book-id", dest="resume_book_id", help="指定 book_id 直接恢复；提供则跳过交互")
    parser.add_argument("--resume-thread-id", dest="resume_thread_id", help="指定 thread_id 恢复（可选）")
    parser.add_argument("--checkpoint-id", dest="checkpoint_id", help="指定 checkpoint_id 回退（可选）")

    # 空库时行为：配置项已移除，固定为自动创建

    # 运行模式
    parser.add_argument("--book-name", dest="book_name", default=None, help="新项目书名（可选，不传则由策划案确定）")

    # 显示
    parser.add_argument(
        "--display", dest="display_mode", choices=["keys", "full"], default=DISPLAY_MODE, help="显示模式"
    )
    parser.add_argument("--max-preview", dest="max_preview_len", type=int, default=MAX_PREVIEW_LEN, help="最大预览长度")
    parser.add_argument(
        "--stream-preview", dest="stream_preview_len", type=int, default=STREAM_PREVIEW_LEN, help="流式预览长度"
    )

    # LLM/文本质量
    parser.add_argument(
        "--min-text-len", dest="llm_text_min_length", type=int, default=LLM_TEXT_MIN_LENGTH, help="LLM文本最小长度"
    )
    parser.add_argument(
        "--sensitive-pattern",
        dest="llm_text_sensitive_patterns",
        action="append",
        help="敏感文本正则，可多次；不传则沿用默认",
    )
    parser.add_argument(
        "--retry-tier",
        dest="llm_text_retry_tier",
        choices=["low", "medium", "high"],
        default=LLM_TEXT_RETRY_TIER,
        help="LLM 重试等级",
    )

    # 其他
    parser.add_argument("--recursion-limit", dest="recursion_limit", type=int, default=RECURSION_LIMIT, help="递归限制")

    return parser.parse_args()


def apply_args(args: argparse.Namespace) -> None:
    global WORKSPACE_PATH, WRITE_CHAPTERS_CNT, ENABLE_INTERRUPT_REVIEW
    global RESUME_BOOK_ID, RESUME_THREAD_ID, RESUME_CHECKPOINT_ID
    global CLI_BOOK_NAME
    global DISPLAY_MODE, MAX_PREVIEW_LEN, STREAM_PREVIEW_LEN
    global LLM_TEXT_MIN_LENGTH, LLM_TEXT_SENSITIVE_PATTERNS, LLM_TEXT_RETRY_TIER
    global RECURSION_LIMIT

    WORKSPACE_PATH = args.workspace or WORKSPACE_PATH
    WRITE_CHAPTERS_CNT = args.write_chapters_cnt or WRITE_CHAPTERS_CNT
    ENABLE_INTERRUPT_REVIEW = args.enable_interrupt_review or ENABLE_INTERRUPT_REVIEW

    RESUME_BOOK_ID = args.resume_book_id or RESUME_BOOK_ID
    RESUME_THREAD_ID = args.resume_thread_id or RESUME_THREAD_ID
    RESUME_CHECKPOINT_ID = args.checkpoint_id or RESUME_CHECKPOINT_ID

    # 空库行为已固定为自动创建，不再读取参数

    CLI_BOOK_NAME = args.book_name or CLI_BOOK_NAME

    DISPLAY_MODE = args.display_mode or DISPLAY_MODE
    MAX_PREVIEW_LEN = args.max_preview_len or MAX_PREVIEW_LEN
    STREAM_PREVIEW_LEN = args.stream_preview_len or STREAM_PREVIEW_LEN

    LLM_TEXT_MIN_LENGTH = args.llm_text_min_length or LLM_TEXT_MIN_LENGTH
    LLM_TEXT_SENSITIVE_PATTERNS = args.llm_text_sensitive_patterns or LLM_TEXT_SENSITIVE_PATTERNS
    LLM_TEXT_RETRY_TIER = args.llm_text_retry_tier or LLM_TEXT_RETRY_TIER

    RECURSION_LIMIT = args.recursion_limit or RECURSION_LIMIT


async def main() -> None:
    """主入口函数"""
    init_log()
    logger = logging.getLogger(__name__)

    # 参数优先
    args = parse_args()
    apply_args(args)

    print("=" * 80)
    print("🚀 QFlowAgent 统一启动器")
    print("=" * 80)

    # 显示当前配置（参数生效后）
    print(f"📋 当前配置：")
    print(f"   工作区路径: {WORKSPACE_PATH}")
    print(f"   写作章节数: {WRITE_CHAPTERS_CNT}")
    print(f"   人工审核: {'启用' if ENABLE_INTERRUPT_REVIEW else '禁用'}")
    if CLI_BOOK_NAME:
        print(f"   指定书名: {CLI_BOOK_NAME}")
    if RESUME_BOOK_ID:
        print(f"   直接恢复: {RESUME_BOOK_ID}")
    print()

    try:
        plan = await build_run_plan()
        await run_qflow(plan)

    except KeyboardInterrupt:
        print("\n👋 用户中断，程序退出")
    except Exception as e:
        logger.error(f"程序执行出错: {e}", exc_info=True)
        print(f"\n❌ 程序执行出错: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
    print("main end")
