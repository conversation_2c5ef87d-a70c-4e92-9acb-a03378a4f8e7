import argparse
import re
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, Iterable, List, Optional, Sequence, Tuple

from src.common.utils.filesystem_utils import FileSystemUtils


def parse_review_file(file_path: Path) -> List[Dict[str, str]]:
    entries: List[Dict[str, str]] = []
    try:
        text = file_path.read_text(encoding="utf-8", errors="ignore")
    except Exception:
        return entries

    lines = text.splitlines()
    heading_pat = re.compile(r"^##\s*(审阅意见|审阅通过|审阅跳过|审阅默认通过)（(.+?)）\s*$")
    meta_pat = re.compile(r"^-\s*(节点|域|文件|卷信息|等级|操作):\s*(.*)\s*$")

    i = 0
    while i < len(lines):
        m = heading_pat.match(lines[i].strip())
        if not m:
            i += 1
            continue
        kind = m.group(1)
        ts = m.group(2)
        meta = {
            "种类": kind,
            "时间": ts,
            "文件": None,
            "节点": None,
            "域": None,
            "卷信息": None,
            "等级": None,
            "操作": None,
        }
        j = i + 1
        while j < len(lines) and lines[j].strip().startswith("-"):
            m2 = meta_pat.match(lines[j].strip())
            if m2:
                meta[m2.group(1)] = m2.group(2)
            j += 1
        meta["来源日志"] = file_path.name
        entries.append(meta)
        i = j

    return entries


def _parse_datetime_flexible(s: Optional[str]) -> Optional[datetime]:
    if s is None:
        return None
    raw = s.strip()
    if not raw:
        return None
    # 兼容以 Z 结尾的 ISO 字符串
    if raw.endswith("Z"):
        raw = raw[:-1] + "+00:00"
    # 常见格式尝试
    fmts: Sequence[str] = (
        "%Y-%m-%d %H:%M:%S.%f%z",
        "%Y-%m-%d %H:%M:%S.%f",
        "%Y-%m-%d %H:%M:%S%z",
        "%Y-%m-%d %H:%M:%S",
        "%Y-%m-%d",
        "%Y/%m/%d %H:%M:%S",
    )
    for fmt in fmts:
        try:
            dt = datetime.strptime(raw, fmt)
            # 无时区信息按本地时间转 UTC 对齐比较
            if dt.tzinfo is None:
                dt = dt.replace(tzinfo=timezone.utc)
            return dt
        except Exception:
            continue
    try:
        dt = datetime.fromisoformat(raw)
        if dt.tzinfo is None:
            dt = dt.replace(tzinfo=timezone.utc)
        return dt
    except Exception:
        return None


def _filter_entries(
    entries: Iterable[Dict[str, str]],
    *,
    nodes: Sequence[str] | None = None,
    domains: Sequence[str] | None = None,
    since: Optional[str] = None,
    until: Optional[str] = None,
) -> List[Dict[str, str]]:
    node_set = set([n.lower() for n in (nodes or [])])
    domain_set = set([d.lower() for d in (domains or [])])
    since_dt = _parse_datetime_flexible(since)
    until_dt = _parse_datetime_flexible(until)

    def _match_one(e: Dict[str, str]) -> bool:
        if node_set:
            node_val = (e.get("节点") or "").lower()
            if not any(n in node_val for n in node_set):
                return False
        if domain_set:
            dom_val = (e.get("域") or "").lower()
            if not any(d in dom_val for d in domain_set):
                return False
        if since_dt or until_dt:
            t = _parse_datetime_flexible(e.get("时间"))
            if t is None:
                return False
            if since_dt and t < since_dt:
                return False
            if until_dt and t > until_dt:
                return False
        return True

    return [e for e in entries if _match_one(e)]


def build_dir_index(dir_path: Path) -> None:
    candidates = [
        dir_path / "审阅记录.md",
        dir_path / "批量-审阅记录.md",
        dir_path / "单卷-审阅记录.md",
    ]
    entries: List[Dict[str, str]] = []
    for p in candidates:
        if p.exists() and p.is_file():
            entries.extend(parse_review_file(p))
    if not entries:
        return
    try:
        entries.sort(key=lambda x: x.get("时间") or "", reverse=True)
    except Exception:
        pass
    lines_out: List[str] = [
        "# 审阅记录索引",
        "",
        f"目录：{dir_path.relative_to(FileSystemUtils.get_workspace_path())}",
        "时间说明：表格中的时间为原日志字符串；用于筛选的时间解析在无时区信息时按 UTC 处理。",
        "",
        "| 时间 | 节点 | 域 | 卷信息 | 等级/操作 | 文件 | 来源日志 |",
        "|---|---|---|---|---|---|---|",
    ]
    for e in entries:
        t = e.get("时间") or "-"
        node = e.get("节点") or "-"
        dom = e.get("域") or "-"
        vol = e.get("卷信息") or "-"
        lev = e.get("等级") or e.get("操作") or "-"
        file_rel = e.get("文件") or "-"
        src = e.get("来源日志") or "-"
        file_link = f"[{file_rel}](./{file_rel})" if file_rel != "-" else "-"
        lines_out.append(f"| {t} | {node} | {dom} | {vol} | {lev} | {file_link} | {src} |")
    out_rel = dir_path.relative_to(FileSystemUtils.get_workspace_path()) / "审阅记录-索引.md"
    FileSystemUtils.write_content_to_workspace(str(out_rel), "\n".join(lines_out), mode="w")


def build_book_global_index(book_root: Path, *, output: Optional[str] = None) -> Optional[Path]:
    entries: List[Dict[str, str]] = []
    for p in book_root.rglob("审阅记录*.md"):
        if p.name.endswith("-索引.md"):
            continue
        entries.extend(parse_review_file(p))
    if not entries:
        return None
    try:
        entries.sort(key=lambda x: x.get("时间") or "", reverse=True)
    except Exception:
        pass
    lines_out: List[str] = [
        "# 审阅记录总索引",
        "",
        f"项目目录：{book_root.relative_to(FileSystemUtils.get_workspace_path())}",
        "时间说明：表格中的时间为原日志字符串；用于筛选的时间解析在无时区信息时按 UTC 处理。",
        "",
        "| 时间 | 节点 | 域 | 卷信息 | 等级/操作 | 文件 | 所在目录 | 来源日志 |",
        "|---|---|---|---|---|---|---|---|",
    ]
    for e in entries:
        t = e.get("时间") or "-"
        node = e.get("节点") or "-"
        dom = e.get("域") or "-"
        vol = e.get("卷信息") or "-"
        lev = e.get("等级") or e.get("操作") or "-"
        file_rel = e.get("文件") or "-"
        # 估算所在目录：文件相对路径的上级（如果有）
        dir_rel = Path(file_rel).parent.as_posix() if file_rel and "/" in file_rel else "."
        src = e.get("来源日志") or "-"
        file_link = f"[{file_rel}](./{file_rel})" if file_rel != "-" else "-"
        lines_out.append(f"| {t} | {node} | {dom} | {vol} | {lev} | {file_link} | {dir_rel} | {src} |")
    default_rel = book_root.relative_to(FileSystemUtils.get_workspace_path()) / "审阅记录-总索引.md"
    out_rel = default_rel
    if output:
        # 仅支持相对 workspace 的路径
        out_path = Path(output)
        if out_path.suffix.lower() == ".md":
            out_rel = out_path
        else:
            out_rel = out_path / default_rel.name
    FileSystemUtils.write_content_to_workspace(str(out_rel), "\n".join(lines_out), mode="w")
    return FileSystemUtils.get_workspace_path() / out_rel


def build_filtered_global_index(
    book_root: Path,
    *,
    nodes: Sequence[str] | None,
    domains: Sequence[str] | None,
    since: Optional[str],
    until: Optional[str],
) -> Optional[Path]:
    entries: List[Dict[str, str]] = []
    for p in book_root.rglob("审阅记录*.md"):
        if p.name.endswith("-索引.md"):
            continue
        entries.extend(parse_review_file(p))
    if not entries:
        return None
    filtered = _filter_entries(entries, nodes=nodes, domains=domains, since=since, until=until)
    if not filtered:
        return None
    try:
        filtered.sort(key=lambda x: x.get("时间") or "", reverse=True)
    except Exception:
        pass
    sel_desc: List[str] = []
    if nodes:
        sel_desc.append("节点=" + ",".join(nodes))
    if domains:
        sel_desc.append("域=" + ",".join(domains))
    if since:
        sel_desc.append("自=" + since)
    if until:
        sel_desc.append("至=" + until)
    header_sel = "，".join(sel_desc) if sel_desc else "(未指定筛选)"

    lines_out: List[str] = [
        "# 审阅记录筛选结果",
        "",
        f"项目目录：{book_root.relative_to(FileSystemUtils.get_workspace_path())}",
        f"筛选条件：{header_sel}",
        "时间说明：若日志时间无时区信息，则按 UTC 解析；无法解析的条目用于筛选时将被忽略。",
        "",
        "| 时间 | 节点 | 域 | 卷信息 | 等级/操作 | 文件 | 所在目录 | 来源日志 |",
        "|---|---|---|---|---|---|---|---|",
    ]
    for e in filtered:
        t = e.get("时间") or "-"
        node = e.get("节点") or "-"
        dom = e.get("域") or "-"
        vol = e.get("卷信息") or "-"
        lev = e.get("等级") or e.get("操作") or "-"
        file_rel = e.get("文件") or "-"
        dir_rel = Path(file_rel).parent.as_posix() if file_rel and "/" in file_rel else "."
        src = e.get("来源日志") or "-"
        file_link = f"[{file_rel}](./{file_rel})" if file_rel != "-" else "-"
        lines_out.append(f"| {t} | {node} | {dom} | {vol} | {lev} | {file_link} | {dir_rel} | {src} |")

    # 输出位置允许自定义，通过 --output 传入（相对 workspace）。
    # 若未指定，则输出到项目根目录。
    return_path = book_root.relative_to(FileSystemUtils.get_workspace_path()) / "审阅记录-筛选结果.md"
    FileSystemUtils.write_content_to_workspace(str(return_path), "\n".join(lines_out), mode="w")
    return return_path


def build_workspace_aggregate_index(
    base: Path,
    *,
    nodes: Sequence[str] | None,
    domains: Sequence[str] | None,
    since: Optional[str],
    until: Optional[str],
    output: Optional[str] = None,
) -> Optional[Path]:
    """
    跨所有项目（book_name/book_id）聚合“审阅记录*.md”，在工作区根目录输出一份总索引：`审阅记录-总索引.md`。
    支持 --node/--domain/--since/--until 过滤。
    """
    entries: List[Dict[str, str]] = []
    # 遍历所有项目目录
    for book_name_dir in [p for p in base.iterdir() if p.is_dir()]:
        for book_dir in [p for p in book_name_dir.iterdir() if p.is_dir()]:
            for p in book_dir.rglob("审阅记录*.md"):
                if p.name.endswith("-索引.md"):
                    continue
                for e in parse_review_file(p):
                    # 增加项目相对路径，便于链接
                    e_copy = dict(e)
                    e_copy["项目目录"] = str(book_dir.relative_to(base))
                    entries.append(e_copy)
    if not entries:
        return None
    # 过滤与排序
    entries = _filter_entries(entries, nodes=nodes, domains=domains, since=since, until=until)
    if not entries:
        return None
    try:
        entries.sort(key=lambda x: x.get("时间") or "", reverse=True)
    except Exception:
        pass

    # 生成内容
    sel_desc: List[str] = []
    if nodes:
        sel_desc.append("节点=" + ",".join(nodes))
    if domains:
        sel_desc.append("域=" + ",".join(domains))
    if since:
        sel_desc.append("自=" + since)
    if until:
        sel_desc.append("至=" + until)
    header_sel = "，".join(sel_desc) if sel_desc else "(未指定筛选)"

    lines_out: List[str] = [
        "# 审阅记录总索引（跨项目）",
        "",
        f"工作区：{base}",
        f"筛选条件：{header_sel}",
        "时间说明：若日志时间无时区信息，则按 UTC 解析；无法解析的条目用于筛选时将被忽略。",
        "",
        "| 时间 | 节点 | 域 | 卷信息 | 等级/操作 | 文件 | 项目目录 | 来源日志 |",
        "|---|---|---|---|---|---|---|---|",
    ]
    for e in entries:
        t = e.get("时间") or "-"
        node = e.get("节点") or "-"
        dom = e.get("域") or "-"
        vol = e.get("卷信息") or "-"
        lev = e.get("等级") or e.get("操作") or "-"
        file_rel = e.get("文件") or "-"
        project_rel = e.get("项目目录") or "-"
        src = e.get("来源日志") or "-"
        # 链接到工作区相对路径
        file_link = f"[{file_rel}](./{project_rel}/{file_rel})" if file_rel != "-" and project_rel != "-" else "-"
        lines_out.append(f"| {t} | {node} | {dom} | {vol} | {lev} | {file_link} | {project_rel} | {src} |")

    default_rel = Path("审阅记录-总索引.md")
    out_rel = default_rel
    if output:
        out_path = Path(output)
        out_rel = out_path if out_path.suffix.lower() == ".md" else out_path / default_rel.name
    FileSystemUtils.write_content_to_workspace(str(out_rel), "\n".join(lines_out), mode="w")
    return FileSystemUtils.get_workspace_path() / out_rel


def main() -> None:
    parser = argparse.ArgumentParser(description="生成审阅记录索引/筛选结果")
    parser.add_argument(
        "book_root", nargs="?", help="书籍根目录（相对 workspace）形如 book_name/book_id；若不指定则扫描所有项目"
    )
    parser.add_argument("--aggregate", action="store_true", help="跨所有项目在工作区根目录生成总索引")
    parser.add_argument("--node", dest="nodes", action="append", help="按节点筛选（可重复）")
    parser.add_argument("--domain", dest="domains", action="append", help="按域筛选（可重复）")
    parser.add_argument("--since", dest="since", help="起始时间，如 2025-01-01 或 2025-01-01 10:00:00")
    parser.add_argument("--until", dest="until", help="截止时间，如 2025-01-31 或 2025-01-31 23:59:59")
    parser.add_argument(
        "--output",
        dest="output",
        help="输出路径（相对 workspace）。用于 --aggregate 总索引或筛选结果；若为目录则使用默认文件名。",
    )
    args = parser.parse_args()

    base = FileSystemUtils.get_workspace_path()
    if args.book_root:
        root = base / args.book_root
        if not root.exists():
            print(f"未找到目录：{root}")
            return
        # 分目录索引
        for d in [p for p in root.iterdir() if p.is_dir()]:
            build_dir_index(d)
        # 总索引
        out_global = build_book_global_index(root, output=args.output)
        if out_global is None:
            print("ℹ️ 该项目无审阅记录，未生成总索引。")
        # 筛选结果（如果指定了任一筛选条件）
        if any([args.nodes, args.domains, args.since, args.until]):
            out = build_filtered_global_index(
                root, nodes=args.nodes, domains=args.domains, since=args.since, until=args.until
            )
            if out:
                # 若指定了 --output，则将筛选结果写入该路径
                if args.output:
                    out_override = Path(args.output)
                    out_final = out_override if out_override.suffix.lower() == ".md" else out_override / out.name
                    content = (FileSystemUtils.get_workspace_path() / out).read_text(encoding="utf-8", errors="ignore")
                    FileSystemUtils.write_content_to_workspace(str(out_final), content, mode="w")
                    print(f"✅ 已生成筛选结果：{FileSystemUtils.get_workspace_path() / out_final}")
                else:
                    print(f"✅ 已生成筛选结果：{out}")
            else:
                print("ℹ️ 按筛选条件未命中任何条目，未生成筛选结果文件。")
        if out_global:
            print(f"✅ 已生成：{out_global}")
        return

    # 未指定项目：遍历所有二级目录（book_name/book_id）
    if args.aggregate:
        out = build_workspace_aggregate_index(
            base, nodes=args.nodes, domains=args.domains, since=args.since, until=args.until, output=args.output
        )
        if out:
            print(f"✅ 已生成跨项目总索引：{out}")
        else:
            print("ℹ️ 工作区内未找到任何审阅记录，未生成总索引。")
        return

    for book_name_dir in [p for p in base.iterdir() if p.is_dir()]:
        for book_dir in [p for p in book_name_dir.iterdir() if p.is_dir()]:
            for d in [p for p in book_dir.iterdir() if p.is_dir()]:
                build_dir_index(d)
            out_global = build_book_global_index(book_dir, output=args.output)
            if out_global is None:
                print(f"ℹ️ 项目无审阅记录：{book_dir.relative_to(base)}")
            # 筛选结果（如果指定了任一筛选条件）
            if any([args.nodes, args.domains, args.since, args.until]):
                out = build_filtered_global_index(
                    book_dir, nodes=args.nodes, domains=args.domains, since=args.since, until=args.until
                )
                if out:
                    if args.output:
                        out_override = Path(args.output)
                        out_final = out_override if out_override.suffix.lower() == ".md" else out_override / out.name
                        content = (FileSystemUtils.get_workspace_path() / out).read_text(
                            encoding="utf-8", errors="ignore"
                        )
                        FileSystemUtils.write_content_to_workspace(str(out_final), content, mode="w")
                        print(f"✅ 已生成筛选结果：{FileSystemUtils.get_workspace_path() / out_final}")
                    else:
                        print(f"✅ 已生成筛选结果：{out}")
                else:
                    print("ℹ️ 按筛选条件未命中任何条目，未生成筛选结果文件。")
            if out_global:
                print(f"✅ 已生成：{out_global}")


if __name__ == "__main__":
    main()
