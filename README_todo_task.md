### 迁移到 LangGraph Task（函数式 API）评估与方案

本文评估在现有 QFlowAgent 中引入（或迁移到）LangGraph 的 task/entrypoint（函数式 API）可行性、收益与落地方案，并结合当前代码现状给出改造优先级与注意事项。

---

### 结论（TL;DR）

- 适配性：可行，且强烈建议采用。当前项目已大量使用 LangGraph（`StateGraph`、`Command`、`Send`、`interrupt`、Postgres checkpointer/store），引入 task/entrypoint 能在不改变核心运行时与持久化模型的前提下，显著简化并发与聚合逻辑。
- 直接收益：
  - 简化并发与聚合：用任务的“声明式并发 + 结果收集”取代手写的 `Send(...)`/join 节点与一组 `state.*_pending/dispatched/result_*` 辅助字段。
  - 类型与边界更清晰：为每个任务定义明确的输入/输出（Pydantic/TypedDict），减少对全局 `state` 的隐式读写，降低状态污染与回收成本。
  - 与 checkpoint 深度协同：任务级持久化更细粒度，恢复/回退更直观。
  - 事件与可观测性继承：现有事件流与快照（`on_task_end`/`on_node_end`）可继续使用，审阅中断（`interrupt`）模式保持不变。
- 风险/成本：
  - 需要重写并发 join 相关节点（大纲细化、场景细化、流式后处理、章节入库）的派发/聚合逻辑。
  - 需要新增 `@entrypoint` 作为主入口替代手工 `StateGraph` 装配；需要更新 `langgraph.json` 指向新的入口。
  - 需调整 `apps/main.py` 的调用方式（调用 entrypoint 亦可流式），以及 `apps/langdev_client.py` 对 assistant_id 的名称对齐。

---

### 现状小结（与 task 的契合点）

- 核心装配：`src/graph/graph.py` 使用 `StateGraph(State, context_schema=RuntimeContext)` 构建主图，检查点/向量存储注入于 `qflow_graph()` 上下文。
- 并发模式：
  - 大纲细化：`writer_outline_refine_one_task` + `writer_outline_join_node`
  - 场景细化：`writer_scene_design_one_task` + `writer_scene_join_node`
  - 流式后处理：`writer_stream_post_one_task` + `writer_stream_post_join_node`
  - 章节入库：`writer_chapter_store_one_task` + `writer_chapter_store_join_node`
  - 以上均通过 `Send(...)` + 手工 join + `state.*expected/*pending/*dispatched/*result_*` 管理在飞并发、超时重派发与限流。
- 人机协作：统一通过 `NodeKit.maybe_interrupt_review()` 使用 `interrupt(...)` 触发 CLI 交互，`apps/event_handler.py` 以 `astream(..., stream_mode=["updates"])` 捕获 `__interrupt__` 并 `Command(resume=...)` 恢复。
- 可观测性：事件快照对 `on_node_end` 与 `on_task_end` 均已兼容（对迁移后任务事件天然友好）。

以上设计与 LangGraph 函数式 API 的理念高度契合：任务边界自然、聚合点清晰、并发控制逻辑完备，可平滑切换为任务范式。

---

### 采用 task 的主要收益

- 简化并发与 join：
  - 以任务的“映射执行 + 聚合”替代当前四处散落的 join 节点与 `_pending/_dispatched/_dispatch_times/_requeue_counts/_result_*` 辅助状态。
  - 失败与重试策略可在任务层封装，聚合端只收集结果，不必维护复杂的在飞/超时队列。
- 状态更“瘦身”：
  - 任务输入/输出显式建模，减少对全局 `state` 的写入（仅在必要时写回汇总产物与 `resume_info`）。
  - 降低 checkpoint 体积与差异噪音，恢复/回退更可控。
- 人审/事件保持不变：
  - 任务内部仍可使用 `interrupt`；事件流继续包含 `on_task_end`，现有快照逻辑可复用。
- 与 CLI/Studio 更自然：
  - `@entrypoint` + `@task` 的结构更适配 `uv run langgraph dev` 与 `langgraph-sdk` 生态。

---

### 改造方案（建议一次性切换，不保留兼容分支）

1) 新建函数式入口（entrypoint）
- 在 `src/graph/` 新增入口模块：以 `@entrypoint` 定义主流程函数，按现有顺序编排节点逻辑（策划 → 设定 → 大纲 → 场景 → 写作/分章）。
- 循环/条件流程（如 `stream_write` ↔ `chapter_segment`）直接用 Python 控制流表达；保持与现状一致的跳转条件与收敛点。

2) 将四类并发改为任务
- 大纲细化（outline）：将 `writer_outline_refine_one_task` 标注为 `@task`，入口批量调用并发执行，收集返回值后写回 `book_detail`，生成批量文件、触发审阅、更新 `resume_info`。
- 场景细化（scene）：同上，将 `writer_scene_design_one_task` 改为 `@task`，保留入库与批量审阅逻辑。
- 流式后处理（stream_post）：将 `writer_stream_post_one_task` 改为 `@task`，在入口根据 `max_concurrency` 限制并发，合并结果后写入批量文件；超时/重试策略在任务层封装（必要时带指数退避/最大重试）。
- 章节入库（chapter_store）：将 `writer_chapter_store_one_task` 改为 `@task`，并发写入数据库，聚合失败清单与汇总行为仅保留在内存/日志（已移除 snapshots）。

3) 任务 I/O 建模
- 输入：避免直接依赖全局 `state`，按需定义任务输入（如 `book_id`、分块/卷号、必要的上下文片段）。
- 输出：返回业务结果的最小集（例如：文件相对路径、文档键、结构化细化结果），聚合端统一写回 `state` 与 `resume_info`。

4) 保留并强化既有能力
- checkpoint/store：继续使用 `get_store_and_checkpointer()` 注入 Postgres checkpointer 与向量存储。
- 审核中断：继续通过 `NodeKit.maybe_interrupt_review()` 使用 `interrupt`；不改 CLI 的 `values` 流处理方式。
- 事件快照：`apps/event_handler.py` 已对 `on_task_end` 做适配，无需额外改动，仅需确认迁移后名称映射仍正确（例如流式后处理/章节入库的友好名称）。

5) 辅助改动
- 更新 `langgraph.json`：指向新的 entrypoint（替换原 `qflow_graph` 暴露）。
- 更新 `apps/main.py`：改为直接调用 entrypoint（固定 `astream(..., stream_mode=["updates"])` 以保持中断交互与事件输出）。
- 更新 `apps/langdev_client.py`：对齐 `assistant_id` 与 `langgraph.json` 的命名。
- 文档更新：同步更新 `README_cmd.md`/`README_nodes.md`/`README_v2.md`/`README_vars.md` 的入口说明与并发章节。

---

### 重点映射（从当前实现到 task）

- 并发派发与限流：
  - 现状：`Send(...)` + join 节点维护 `_pending/_dispatched/_dispatch_times/_requeue_counts`，并在 `join` 内部分批追加 `Send(...)`。
  - 目标：入口一次性构造任务列表，按 `max_concurrency` 控制并发执行与重试；结果聚合后统一写回，省去中间状态键。

- 结果聚合：
  - 现状：通过扫描 `state` 的 `*_result_*` 键收集并排序。
  - 目标：直接收集任务返回值（列表/字典），在聚合端按卷号/索引排序写入 `book_detail` 与批量文件。

- 失败与重试：
  - 现状：超时 TTL + 计数 + 降级占位结果，避免无限等待。
  - 目标：在任务层提供最大重试与超时，聚合端仅区分成功/失败并记录失败清单（已移除 snapshots 写入）。

- resume_info 与快照：已移除快照，仅保留 resume_info。
  - 继续在聚合端统一写入 `resume_info`，快照命名与落盘路径保持不变，保障 CLI 体验一致。

---

### 影响范围与注意事项

- 事件名称与映射：`apps/event_handler.py` 的友好名称中包含任务名（如 `writer_stream_post_one`）；迁移后任务名应保持一致或在映射中同步更新。
- CLI 恢复逻辑：基于 checkpointer 的恢复/回退流程（`apps/main.py` 与 `src/nodes/progress/progress_functional.py`）不需改变，只需确保 entrypoint 的 `State` 与 `resume_info` 行为一致。
- 递归上限与循环：`recursion_limit` 继续放在 config 顶层；函数式入口中的循环需显式 `break/continue`，避免隐式无限跳转。
- 版本约束：项目已使用 `langgraph>=0.5.4` 与 `langgraph-api`，满足函数式 API 与 dev/SDK 使用需求；若采用新特性（并发参数/任务分组），按需微调版本。

---

### 落地步骤（建议拆解）

- 第 1 步（骨架）：增设 `@entrypoint` 主入口，串起单线程路径（不含并发），打通 checkpoint/审阅/事件流；`uv run apps/main.py` 与 `uv run langgraph dev --allow-blocking` 均可跑通。
- 第 2 步（并发一）：将“大纲细化”替换为任务并发与聚合，删除对应 join 与派发辅助键。
- 第 3 步（并发二）：迁移“场景细化”。
- 第 4 步（并发三）：迁移“流式后处理”（含失败降级与批量汇总快照）。
- 第 5 步（并发四）：迁移“章节入库”。
- 第 6 步（清理/文档/测试）：
  - 移除已废弃的 join/派发/队列状态键与相关代码。
  - 更新 `README_cmd.md`/`README_nodes.md` 等文档的并发与入口章节。
  - 回归测试：
    - 交互审阅闭环（`/pass`、`/skip`、`/high|/medium|/low`）
    - checkpoint 恢复/回退/分支
    - 事件快照与汇总文件
    - 写作→分章的循环收敛

---

### 验收标准（建议）

- 开发与运行：
  - `uv run apps/main.py` 正常；
  - `uv run langgraph dev --allow-blocking` 可启动，`apps/langdev_client.py` 能流式查看并与中断交互；
  - `langgraph.json` 指向新的 entrypoint，`assistant_id` 与客户端一致。
- 功能不回退：
  - 产物落盘（单卷/批量文件）与入库（向量检索）行为一致；
  - 审阅中断、`resume_info`、事件快照与原有展示一致或更清晰；
  - checkpoint 恢复/回退路径与当前匹配。
- 代码质量：
  - 并发与聚合代码行数显著减少，状态键更少更聚焦；
  - 任务输入/输出具备清晰的 Pydantic/TypedDict 定义；
  - 无冗余兼容代码（旧 join/派发逻辑删除）。

---

### 附注

- 迁移过程中，建议优先完成“最重/最乱”的并发（流式后处理与章节入库），它们的收益最大，也能率先验证任务范式对可观测性与稳定性的提升。
- 若短期只做试点，可先在“大纲细化/场景细化”落地，不改变主干流程，验证端到端可用性后再全量替换。


