问题描述：

首次运行遇到Recursion limit of 25 reached 报错后，再次恢复小说，仍然报错。这与我预期的恢复功能不符，如何处理，需要分叉么？


问题日志：
```
2025-08-10 14:13:14,080 [__main__] ERROR: 程序执行出错: Recursion limit of 25 reached without hitting a stop condition. You can increase the limit by setting the `recursion_limit` config key.
For troubleshooting, visit: https://python.langchain.com/docs/troubleshooting/errors/GRAPH_RECURSION_LIMIT
Traceback (most recent call last):
  File "/Users/<USER>/yeran/dev/nemoai/QFlowAgent/apps/main.py", line 402, in main
    await run_with_review_selection()
  File "/Users/<USER>/yeran/dev/nemoai/QFlowAgent/apps/main.py", line 184, in run_with_review_selection
    await run_graph_new_book()
  File "/Users/<USER>/yeran/dev/nemoai/QFlowAgent/apps/main.py", line 237, in run_graph_new_book
    await stream_graph_events(graph, llm_input, config)
  File "/Users/<USER>/yeran/dev/nemoai/QFlowAgent/apps/cli_events.py", line 363, in stream_graph_events
    async for event in graph.astream(
  File "/Users/<USER>/yeran/dev/nemoai/QFlowAgent/.venv/lib/python3.12/site-packages/langgraph/pregel/main.py", line 2975, in astream
    raise GraphRecursionError(msg)
langgraph.errors.GraphRecursionError: Recursion limit of 25 reached without hitting a stop condition. You can increase the limit by setting the `recursion_limit` config key.
For troubleshooting, visit: https://python.langchain.com/docs/troubleshooting/errors/GRAPH_RECURSION_LIMIT

❌ 程序执行出错: Recursion limit of 25 reached without hitting a stop condition. You can increase the limit by setting the `recursion_limit` config key.
For troubleshooting, visit: https://python.langchain.com/docs/troubleshooting/errors/GRAPH_RECURSION_LIMIT
Traceback (most recent call last):
  File "/Users/<USER>/yeran/dev/nemoai/QFlowAgent/apps/main.py", line 413, in <module>
    asyncio.run(main())
  File "/opt/homebrew/Cellar/python@3.12/3.12.4/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 194, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.4/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.4/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/base_events.py", line 687, in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
  File "/Users/<USER>/yeran/dev/nemoai/QFlowAgent/apps/main.py", line 402, in main
    await run_with_review_selection()
  File "/Users/<USER>/yeran/dev/nemoai/QFlowAgent/apps/main.py", line 184, in run_with_review_selection
    await run_graph_new_book()
  File "/Users/<USER>/yeran/dev/nemoai/QFlowAgent/apps/main.py", line 237, in run_graph_new_book
    await stream_graph_events(graph, llm_input, config)
  File "/Users/<USER>/yeran/dev/nemoai/QFlowAgent/apps/cli_events.py", line 363, in stream_graph_events
    async for event in graph.astream(
  File "/Users/<USER>/yeran/dev/nemoai/QFlowAgent/.venv/lib/python3.12/site-packages/langgraph/pregel/main.py", line 2975, in astream
    raise GraphRecursionError(msg)
langgraph.errors.GraphRecursionError: Recursion limit of 25 reached without hitting a stop condition. You can increase the limit by setting the `recursion_limit` config key.
For troubleshooting, visit: https://python.langchain.com/docs/troubleshooting/errors/GRAPH_RECURSION_LIMIT
(QFlowAgent) Mac:QFlowAgent nemo$ 
(QFlowAgent) Mac:QFlowAgent nemo$ uv run -q apps/main.py --no-review --chapters 0 --display keys --max-preview 80 --stream-preview 40 --recursion-limit 5
================================================================================
🚀 QFlowAgent 统一启动器
================================================================================
📋 当前配置：
   工作区路径: workspace
   写作章节数: 0
   人工审核: 禁用


🎮 交互式选择模式

================================================================================
📚 发现以下进行中的小说项目:
================================================================================
1. 【时空之主：万界囚笼】1cae2a3d-c16c-48b8-9b51-b50fd956e14c 创建时间：2025-08-10 14:05，更新时间：2025-08-10 14:13

0. 创建新的小说项目
D. 删除小说项目

请输入选择 (0-1 / D): 1

可选的历史 checkpoint（树形，新→旧按分支排序）：
1. └─ 1f075b01-9039-61e4-8002-d3a11301d0e7 @ 2025-08-10 14:06 (thread: ) | 
2. └─ 1f075b03-2d4d-64fc-8003-37dcd417a9bc @ 2025-08-10 14:06 (thread: ) | 
3. └─ 1f075b04-d5c3-6540-8004-fd19d8dbc52d @ 2025-08-10 14:07 (thread: ) | 
4. └─ 1f075b06-36c9-615e-8005-b45d4f22623e @ 2025-08-10 14:08 (thread: ) | 
5. └─ 1f075b07-c7bc-6336-8006-c8f68c33f064 @ 2025-08-10 14:08 (thread: ) | 
6. └─ 1f075b08-c889-66dc-8007-792d7e5e15e1 @ 2025-08-10 14:09 (thread: ) | 
7. └─ 1f075b0a-9d48-65f2-8008-63428201fa5a @ 2025-08-10 14:10 (thread: ) | 
8. └─ 1f075b0b-a5a8-6746-8009-dfdceeb2e25a @ 2025-08-10 14:10 (thread: ) | 
9. └─ 1f075b0e-1756-6968-800a-456fc8077f26 @ 2025-08-10 14:11 (thread: ) | 
10. └─ 1f075b0f-2a63-63a2-800b-d33dd64cf02c @ 2025-08-10 14:12 (thread: ) | 
11. └─ 1f075b10-ca2f-68b2-800c-20f7df8b5114 @ 2025-08-10 14:12 (thread: ) | 
12. └─ 1f075b11-9211-68da-800d-dec1727cb16f @ 2025-08-10 14:13 (thread: ) | 
13. └─ 1f075b11-9214-624c-800e-4f50e7c58ed7 @ 2025-08-10 14:13 (thread: ) | 
14. └─ 1f075b11-9215-62dc-800f-3378afc2c1ea @ 2025-08-10 14:13 (thread: ) | 
15. └─ 1f075b11-9216-6b28-8010-d4cca9e24674 @ 2025-08-10 14:13 (thread: ) | 
16. └─ 1f075b11-9218-669e-8011-2832ef3b9187 @ 2025-08-10 14:13 (thread: ) | 
17. └─ 1f075b11-9219-636e-8012-e5b1e313157e @ 2025-08-10 14:13 (thread: ) | 
18. └─ 1f075b11-921a-6048-8013-ac1c6b462289 @ 2025-08-10 14:13 (thread: ) | 
19. └─ 1f075b11-921b-622c-8014-faca4ff7d782 @ 2025-08-10 14:13 (thread: ) | 
20. └─ 1f075b11-921b-6e84-8015-1fac569771f1 @ 2025-08-10 14:13 (thread: ) | 
21. └─ 1f075b11-921c-67a8-8016-2a24e1a10d5e @ 2025-08-10 14:13 (thread: ) | 
22. └─ 1f075b11-921d-6d42-8017-1f55ba62a587 @ 2025-08-10 14:13 (thread: ) | 
23. └─ 1f075b11-921e-6a8a-8018-84b1745c434b @ 2025-08-10 14:13 (thread: ) | 
24. └─ 1f075b11-921f-6642-8019-93b79e7abe01 @ 2025-08-10 14:13 (thread: ) | 
0. 使用最新状态（不回退）

请选择 checkpoint (0-24): 0

🔄 恢复项目：book_id=1cae2a3d-c16c-48b8-9b51-b50fd956e14c，thread_id=1cae2a3d-c16c-48b8-9b51-b50fd956e14c，书名=时空之主：万界囚笼

2025-08-10 14:17:18,892 [src.tools.mcps.sequential_thinking] INFO: init_mcp_tools(): placeholder no-op initialized
🔍 正在扫描数据库中的小说项目...

================================================================================
📚 发现以下进行中的小说项目:
================================================================================
1. 【时空之主：万界囚笼】1cae2a3d-c16c-48b8-9b51-b50fd956e14c 创建时间：2025-08-10 14:05，更新时间：2025-08-10 14:17

0. 创建新的小说项目
D. 删除小说项目

请输入选择 (0-1 / D): 1

可选的历史 checkpoint（树形，新→旧按分支排序）：
1. └─ 1f075b01-9039-61e4-8002-d3a11301d0e7 @ 2025-08-10 14:06 (thread: ) | 
2. └─ 1f075b03-2d4d-64fc-8003-37dcd417a9bc @ 2025-08-10 14:06 (thread: ) | 
3. └─ 1f075b04-d5c3-6540-8004-fd19d8dbc52d @ 2025-08-10 14:07 (thread: ) | 
4. └─ 1f075b06-36c9-615e-8005-b45d4f22623e @ 2025-08-10 14:08 (thread: ) | 
5. └─ 1f075b07-c7bc-6336-8006-c8f68c33f064 @ 2025-08-10 14:08 (thread: ) | 
6. └─ 1f075b08-c889-66dc-8007-792d7e5e15e1 @ 2025-08-10 14:09 (thread: ) | 
7. └─ 1f075b0a-9d48-65f2-8008-63428201fa5a @ 2025-08-10 14:10 (thread: ) | 
8. └─ 1f075b0b-a5a8-6746-8009-dfdceeb2e25a @ 2025-08-10 14:10 (thread: ) | 
9. └─ 1f075b0e-1756-6968-800a-456fc8077f26 @ 2025-08-10 14:11 (thread: ) | 
10. └─ 1f075b0f-2a63-63a2-800b-d33dd64cf02c @ 2025-08-10 14:12 (thread: ) | 
11. └─ 1f075b10-ca2f-68b2-800c-20f7df8b5114 @ 2025-08-10 14:12 (thread: ) | 
12. └─ 1f075b11-9211-68da-800d-dec1727cb16f @ 2025-08-10 14:13 (thread: ) | 
13. └─ 1f075b11-9214-624c-800e-4f50e7c58ed7 @ 2025-08-10 14:13 (thread: ) | 
14. └─ 1f075b11-9215-62dc-800f-3378afc2c1ea @ 2025-08-10 14:13 (thread: ) | 
15. └─ 1f075b11-9216-6b28-8010-d4cca9e24674 @ 2025-08-10 14:13 (thread: ) | 
16. └─ 1f075b11-9218-669e-8011-2832ef3b9187 @ 2025-08-10 14:13 (thread: ) | 
17. └─ 1f075b11-9219-636e-8012-e5b1e313157e @ 2025-08-10 14:13 (thread: ) | 
18. └─ 1f075b11-921a-6048-8013-ac1c6b462289 @ 2025-08-10 14:13 (thread: ) | 
19. └─ 1f075b11-921b-622c-8014-faca4ff7d782 @ 2025-08-10 14:13 (thread: ) | 
20. └─ 1f075b11-921b-6e84-8015-1fac569771f1 @ 2025-08-10 14:13 (thread: ) | 
21. └─ 1f075b11-921c-67a8-8016-2a24e1a10d5e @ 2025-08-10 14:13 (thread: ) | 
22. └─ 1f075b11-921d-6d42-8017-1f55ba62a587 @ 2025-08-10 14:13 (thread: ) | 
23. └─ 1f075b11-921e-6a8a-8018-84b1745c434b @ 2025-08-10 14:13 (thread: ) | 
24. └─ 1f075b11-921f-6642-8019-93b79e7abe01 @ 2025-08-10 14:13 (thread: ) | 
25. └─ 1f075b1a-b1e2-664c-801a-416028cb8130 @ 2025-08-10 14:17 (thread: ) | 
26. └─ 1f075b1a-b1e3-6e52-801b-19325ed6564a @ 2025-08-10 14:17 (thread: ) | 
0. 使用最新状态（不回退）

请选择 checkpoint (0-26): 0
2025-08-10 14:17:25,700 [src.nodes.common.node_kit] INFO: 保存文件: 时空之主：万界囚笼/1cae2a3d-c16c-48b8-9b51-b50fd956e14c/character/角色详情.md
2025-08-10 14:17:25,702 [src.nodes.common.node_kit] INFO: 保存文件: 时空之主：万界囚笼/1cae2a3d-c16c-48b8-9b51-b50fd956e14c/character/角色详情.md
2025-08-10 14:17:25,703 [src.nodes.common.node_kit] INFO: 保存文件: 时空之主：万界囚笼/1cae2a3d-c16c-48b8-9b51-b50fd956e14c/character/角色详情.md
2025-08-10 14:17:25,704 [src.nodes.common.node_kit] INFO: 保存文件: 时空之主：万界囚笼/1cae2a3d-c16c-48b8-9b51-b50fd956e14c/character/角色详情.md
2025-08-10 14:17:25,706 [src.nodes.common.node_kit] INFO: 保存文件: 时空之主：万界囚笼/1cae2a3d-c16c-48b8-9b51-b50fd956e14c/character/角色详情.md
2025-08-10 14:17:25,707 [src.nodes.common.node_kit] INFO: 保存文件: 时空之主：万界囚笼/1cae2a3d-c16c-48b8-9b51-b50fd956e14c/character/角色详情.md
2025-08-10 14:17:25,714 [src.nodes.common.node_kit] INFO: 保存文件: 时空之主：万界囚笼/1cae2a3d-c16c-48b8-9b51-b50fd956e14c/character/角色详情.md
2025-08-10 14:17:25,715 [src.nodes.common.node_kit] INFO: 保存文件: 时空之主：万界囚笼/1cae2a3d-c16c-48b8-9b51-b50fd956e14c/character/角色详情.md
2025-08-10 14:17:25,884 [__main__] ERROR: 程序执行出错: Recursion limit of 25 reached without hitting a stop condition. You can increase the limit by setting the `recursion_limit` config key.
For troubleshooting, visit: https://python.langchain.com/docs/troubleshooting/errors/GRAPH_RECURSION_LIMIT
Traceback (most recent call last):
  File "/Users/<USER>/yeran/dev/nemoai/QFlowAgent/apps/main.py", line 402, in main
    await run_with_review_selection()
  File "/Users/<USER>/yeran/dev/nemoai/QFlowAgent/apps/main.py", line 219, in run_with_review_selection
    await run_graph_resume(thread_id=result.thread_id)
  File "/Users/<USER>/yeran/dev/nemoai/QFlowAgent/apps/main.py", line 247, in run_graph_resume
    await stream_graph_events(graph, llm_input, config)
  File "/Users/<USER>/yeran/dev/nemoai/QFlowAgent/apps/cli_events.py", line 365, in stream_graph_events
    async for event in graph.astream(
  File "/Users/<USER>/yeran/dev/nemoai/QFlowAgent/.venv/lib/python3.12/site-packages/langgraph/pregel/main.py", line 2975, in astream
    raise GraphRecursionError(msg)
langgraph.errors.GraphRecursionError: Recursion limit of 25 reached without hitting a stop condition. You can increase the limit by setting the `recursion_limit` config key.
For troubleshooting, visit: https://python.langchain.com/docs/troubleshooting/errors/GRAPH_RECURSION_LIMIT

❌ 程序执行出错: Recursion limit of 25 reached without hitting a stop condition. You can increase the limit by setting the `recursion_limit` config key.
For troubleshooting, visit: https://python.langchain.com/docs/troubleshooting/errors/GRAPH_RECURSION_LIMIT
Traceback (most recent call last):
  File "/Users/<USER>/yeran/dev/nemoai/QFlowAgent/apps/main.py", line 413, in <module>
    asyncio.run(main())
  File "/opt/homebrew/Cellar/python@3.12/3.12.4/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 194, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.4/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.4/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/base_events.py", line 687, in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
  File "/Users/<USER>/yeran/dev/nemoai/QFlowAgent/apps/main.py", line 402, in main
    await run_with_review_selection()
  File "/Users/<USER>/yeran/dev/nemoai/QFlowAgent/apps/main.py", line 219, in run_with_review_selection
    await run_graph_resume(thread_id=result.thread_id)
  File "/Users/<USER>/yeran/dev/nemoai/QFlowAgent/apps/main.py", line 247, in run_graph_resume
    await stream_graph_events(graph, llm_input, config)
  File "/Users/<USER>/yeran/dev/nemoai/QFlowAgent/apps/cli_events.py", line 365, in stream_graph_events
    async for event in graph.astream(
  File "/Users/<USER>/yeran/dev/nemoai/QFlowAgent/.venv/lib/python3.12/site-packages/langgraph/pregel/main.py", line 2975, in astream
    raise GraphRecursionError(msg)
langgraph.errors.GraphRecursionError: Recursion limit of 25 reached without hitting a stop condition. You can increase the limit by setting the `recursion_limit` config key.
For troubleshooting, visit: https://python.langchain.com/docs/troubleshooting/errors/GRAPH_RECURSION_LIMIT
(QFlowAgent) Mac:QFlowAgent nemo$ 
```